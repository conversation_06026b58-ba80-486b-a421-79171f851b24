<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code لتطبيق الحج على GitHub</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2E7D32;
            margin: 0;
            font-size: 28px;
        }
        
        .kaaba-icon {
            width: 60px;
            height: 60px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 20px;
            font-size: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            direction: ltr;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .qr-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
            display: none;
        }
        
        .download-btn {
            background: #2196F3;
            margin-top: 10px;
        }
        
        .example {
            background: #E8F5E8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-right: 4px solid #4CAF50;
        }
        
        .example h4 {
            margin-top: 0;
            color: #2E7D32;
        }
        
        .url-example {
            background: white;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 14px;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="kaaba-icon">🕋</div>
            <h1>QR Code لتطبيق الحج على GitHub</h1>
        </div>
        
        <div class="input-group">
            <label for="githubUrl">رابط GitHub Pages:</label>
            <input type="url" id="githubUrl" placeholder="https://username.github.io/hajj-tasks-app-1446" />
        </div>
        
        <button class="btn" onclick="generateQR()">إنشاء QR Code</button>
        
        <div id="qrContainer" class="qr-container">
            <h3>QR Code جاهز للطباعة والمشاركة</h3>
            <div id="qrCode"></div>
            <button class="btn download-btn" onclick="downloadQR()">تحميل QR Code</button>
        </div>
        
        <div class="example">
            <h4>مثال على رابط GitHub Pages:</h4>
            <div class="url-example">https://your-username.github.io/hajj-tasks-app-1446</div>
            <p><strong>ملاحظة:</strong> غيّر "your-username" إلى اسم المستخدم الخاص بك على GitHub</p>
        </div>
        
        <div class="example">
            <h4>خطوات الحصول على الرابط:</h4>
            <ol>
                <li>ارفع التطبيق على GitHub</li>
                <li>فعّل GitHub Pages في Settings</li>
                <li>انتظر 2-5 دقائق</li>
                <li>انسخ الرابط والصقه أعلاه</li>
                <li>أنشئ QR Code</li>
            </ol>
        </div>
        
        <div class="example">
            <h4>للحجاج:</h4>
            <ul>
                <li>امسح QR Code بكاميرا الهاتف</li>
                <li>اضغط على الرابط</li>
                <li>في الآيفون: "إضافة للشاشة الرئيسية"</li>
                <li>في الأندرويد: "إضافة للشاشة الرئيسية"</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        function generateQR() {
            const url = document.getElementById('githubUrl').value;
            
            if (!url) {
                alert('يرجى إدخال رابط GitHub Pages');
                return;
            }
            
            if (!url.includes('github.io')) {
                alert('يرجى إدخال رابط GitHub Pages صحيح (يحتوي على github.io)');
                return;
            }
            
            const qrContainer = document.getElementById('qrContainer');
            const qrCodeDiv = document.getElementById('qrCode');
            
            qrCodeDiv.innerHTML = '';
            
            QRCode.toCanvas(qrCodeDiv, url, {
                width: 300,
                height: 300,
                color: {
                    dark: '#2E7D32',
                    light: '#FFFFFF'
                },
                margin: 2
            }, function (error) {
                if (error) {
                    alert('حدث خطأ في إنشاء QR Code');
                    console.error(error);
                } else {
                    qrContainer.style.display = 'block';
                    
                    const infoDiv = document.createElement('div');
                    infoDiv.innerHTML = `
                        <div style="margin-top: 15px; padding: 15px; background: white; border-radius: 8px; border: 2px solid #4CAF50;">
                            <h4 style="margin: 0 0 10px 0; color: #2E7D32;">📱 تطبيق المهام اليومية للحج</h4>
                            <p style="margin: 5px 0; color: #666;">حملة الفرقان 1446 هـ</p>
                            <p style="margin: 5px 0; font-size: 12px; color: #888; direction: ltr; text-align: center;">${url}</p>
                            <p style="margin: 10px 0 0 0; font-size: 14px; color: #333;">امسح الكود للحصول على التطبيق</p>
                        </div>
                    `;
                    qrCodeDiv.appendChild(infoDiv);
                }
            });
        }
        
        function downloadQR() {
            const canvas = document.querySelector('#qrCode canvas');
            if (!canvas) {
                alert('لا يوجد QR Code للتحميل');
                return;
            }
            
            const finalCanvas = document.createElement('canvas');
            const ctx = finalCanvas.getContext('2d');
            
            finalCanvas.width = 400;
            finalCanvas.height = 500;
            
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
            
            ctx.drawImage(canvas, 50, 50, 300, 300);
            
            ctx.fillStyle = '#2E7D32';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('تطبيق المهام اليومية للحج', 200, 30);
            
            ctx.fillStyle = '#666666';
            ctx.font = '18px Arial';
            ctx.fillText('حملة الفرقان 1446 هـ', 200, 380);
            
            ctx.fillStyle = '#333333';
            ctx.font = '16px Arial';
            ctx.fillText('امسح الكود للحصول على التطبيق', 200, 410);
            
            ctx.font = '30px Arial';
            ctx.fillText('🕋', 200, 450);
            
            const link = document.createElement('a');
            link.download = 'hajj-app-github-qr-code.png';
            link.href = finalCanvas.toDataURL();
            link.click();
        }
        
        document.getElementById('githubUrl').focus();
    </script>
</body>
</html>
