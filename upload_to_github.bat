@echo off
echo ========================================
echo      رفع تطبيق الحج إلى GitHub
echo    Hajj Tasks App - GitHub Upload
echo ========================================
echo.

cd hajj-tasks-app-1446

echo [1/5] التحقق من حالة Git...
git status
echo.

echo [2/5] إدخال معلومات GitHub...
echo.
echo يرجى إدخال اسم المستخدم على GitHub:
set /p USERNAME="GitHub Username: "
echo.

echo [3/5] إضافة رابط GitHub...
git remote remove origin 2>nul
git remote add origin https://github.com/%USERNAME%/hajj-tasks-app-1446.git
echo تم إضافة الرابط: https://github.com/%USERNAME%/hajj-tasks-app-1446.git
echo.

echo [4/5] تأكيد اسم الفرع...
git branch -M main
echo تم تغيير اسم الفرع إلى main
echo.

echo [5/5] رفع الملفات إلى GitHub...
echo.
echo ملاحظة: قد تحتاج لتسجيل الدخول في GitHub
echo إذا فشل الرفع، استخدم GitHub Desktop أو Personal Access Token
echo.
git push -u origin main

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo        تم الرفع بنجاح! 🎉
    echo ========================================
    echo.
    echo 🌐 رابط المستودع:
    echo https://github.com/%USERNAME%/hajj-tasks-app-1446
    echo.
    echo 📱 لتفعيل GitHub Pages:
    echo 1. اذهب للرابط أعلاه
    echo 2. Settings ^> Pages
    echo 3. Source: Deploy from a branch
    echo 4. Branch: main
    echo 5. Folder: / ^(root^)
    echo 6. Save
    echo.
    echo 🚀 رابط التطبيق سيكون:
    echo https://%USERNAME%.github.io/hajj-tasks-app-1446
    echo.
    echo ⏰ انتظر 2-5 دقائق حتى يصبح الرابط جاهزاً
) else (
    echo.
    echo ========================================
    echo         فشل في الرفع ❌
    echo ========================================
    echo.
    echo 💡 الحلول المقترحة:
    echo.
    echo 1. تأكد من إنشاء المستودع على GitHub أولاً:
    echo    https://github.com/new
    echo    اسم المستودع: hajj-tasks-app-1446
    echo.
    echo 2. استخدم GitHub Desktop ^(الأسهل^):
    echo    - حمّل من: https://desktop.github.com/
    echo    - سجل دخول بحسابك
    echo    - Add Existing Repository
    echo    - اختر مجلد hajj-tasks-app-1446
    echo    - Publish repository
    echo.
    echo 3. أو أنشئ Personal Access Token:
    echo    GitHub ^> Settings ^> Developer settings ^> Personal access tokens
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
