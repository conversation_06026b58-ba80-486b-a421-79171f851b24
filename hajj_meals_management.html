<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة وجبات الحج - Hajj Meals Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .datetime-display {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.1em;
            font-weight: 600;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .language-toggle {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .language-toggle:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            position: relative;
        }

        .filter-btn.active {
            background: #27ae60;
            color: white;
        }

        .filter-btn .hall-count {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.8em;
            position: absolute;
            top: -5px;
            right: -5px;
            min-width: 20px;
            text-align: center;
        }

        .settings-btn {
            background: #9b59b6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .settings-btn:hover {
            background: #8e44ad;
            transform: translateY(-2px);
        }

        .meals-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .meals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .meal-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .meal-card:hover {
            transform: translateY(-5px);
        }

        .meal-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .meal-time {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .halls-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .halls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .hall-card {
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .hall-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .hall-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .hall-card.waiting {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .hall-card.completed {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .hall-card.preparing {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            color: #333;
        }

        .notifications {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            pointer-events: none;
        }

        .notification {
            background: #2ecc71;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.5s ease;
            pointer-events: auto;
        }

        .notification.warning {
            background: #f39c12;
        }

        .notification.error {
            background: #e74c3c;
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .stats {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* Settings Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 5% auto;
            padding: 0;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 30px;
            border-radius: 20px 20px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            color: #2c3e50;
            margin: 0;
            font-size: 1.8em;
        }

        .close {
            color: #e74c3c;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close:hover {
            color: #c0392b;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 30px;
            color: white;
        }

        .settings-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .settings-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #fff;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px 15px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #27ae60;
            color: white;
        }

        .btn-primary:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .hall-status-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .status-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
            color: white;
        }

        .status-btn.active-status {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .status-btn.waiting-status {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .status-btn.completed-status {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .status-btn.preparing-status {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            color: #333;
        }

        .status-btn.selected {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        /* RTL/LTR Support */
        [dir="ltr"] {
            text-align: left;
        }

        [dir="ltr"] .header {
            text-align: center;
        }

        [dir="ltr"] .controls {
            flex-direction: row-reverse;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-buttons {
                justify-content: center;
            }

            .meals-grid {
                grid-template-columns: 1fr;
            }

            .halls-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="notifications" id="notifications"></div>
    
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1 id="main-title">🕋 نظام إدارة وجبات الحج</h1>
            <p class="subtitle" id="subtitle">إدارة شاملة لوجبات الحجاج في المشاعر المقدسة</p>
            <div class="datetime-display" id="datetime-display"></div>
        </div>

        <!-- Controls Section -->
        <div class="controls">
            <div style="display: flex; gap: 15px; align-items: center;">
                <button class="language-toggle" id="language-toggle" onclick="toggleLanguage()">
                    English
                </button>
                <button class="settings-btn" id="settings-btn" onclick="openSettingsModal()">
                    ⚙️ <span id="settings-text">ضبط الوجبات</span>
                </button>
            </div>

            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterHalls('all')" id="filter-all">
                    الكل <span class="hall-count" id="count-all">12</span>
                </button>
                <button class="filter-btn" onclick="filterHalls('active')" id="filter-active">
                    نشطة <span class="hall-count" id="count-active">0</span>
                </button>
                <button class="filter-btn" onclick="filterHalls('waiting')" id="filter-waiting">
                    انتظار <span class="hall-count" id="count-waiting">0</span>
                </button>
                <button class="filter-btn" onclick="filterHalls('completed')" id="filter-completed">
                    مكتملة <span class="hall-count" id="count-completed">0</span>
                </button>
                <button class="filter-btn" onclick="filterHalls('preparing')" id="filter-preparing">
                    في الاستعداد <span class="hall-count" id="count-preparing">0</span>
                </button>
            </div>
        </div>

        <!-- Meals Management Section -->
        <div class="meals-section">
            <h2 id="meals-title">🍽️ إدارة الوجبات</h2>
            <div class="meals-grid" id="meals-grid">
                <!-- Meals will be populated by JavaScript -->
            </div>
        </div>

        <!-- Halls Status Section -->
        <div class="halls-section">
            <h2 id="halls-title">🏛️ حالة القاعات</h2>
            <div class="halls-grid" id="halls-grid">
                <!-- Halls will be populated by JavaScript -->
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="stats">
            <h2 id="stats-title">📊 الإحصائيات</h2>
            <div class="stats-grid" id="stats-grid">
                <!-- Stats will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">⚙️ ضبط الوجبات والقاعات</h2>
                <span class="close" onclick="closeSettingsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Meals Settings -->
                <div class="settings-section">
                    <h3 id="meals-settings-title">🍽️ إعدادات الوجبات</h3>
                    <div id="meals-settings">
                        <!-- Meals will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Halls Settings -->
                <div class="settings-section">
                    <h3 id="halls-settings-title">🏛️ إعدادات القاعات</h3>
                    <div id="halls-settings">
                        <!-- Halls will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="saveSettings()" id="save-btn">💾 حفظ التغييرات</button>
                    <button class="btn btn-secondary" onclick="resetSettings()" id="reset-btn">🔄 إعادة تعيين</button>
                    <button class="btn btn-secondary" onclick="closeSettingsModal()" id="cancel-btn">❌ إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Language and translations
        let currentLanguage = 'ar';

        const translations = {
            ar: {
                mainTitle: '🕋 نظام إدارة وجبات الحج',
                subtitle: 'إدارة شاملة لوجبات الحجاج في المشاعر المقدسة',
                languageToggle: 'English',
                filterAll: 'الكل',
                filterActive: 'نشطة',
                filterWaiting: 'انتظار',
                filterCompleted: 'مكتملة',
                filterPreparing: 'في الاستعداد',
                mealsTitle: '🍽️ إدارة الوجبات',
                hallsTitle: '🏛️ حالة القاعات',
                statsTitle: '📊 الإحصائيات',
                breakfast: 'الإفطار',
                lunch: 'الغداء',
                dinner: 'العشاء',
                time: 'الوقت',
                duration: 'المدة',
                minutes: 'دقيقة',
                hall: 'قاعة',
                capacity: 'السعة',
                current: 'الحالي',
                status: 'الحالة',
                active: 'نشطة',
                waiting: 'انتظار',
                completed: 'مكتملة',
                preparing: 'في الاستعداد',
                totalHalls: 'إجمالي القاعات',
                activeHalls: 'القاعات النشطة',
                servedMeals: 'الوجبات المقدمة',
                totalCapacity: 'السعة الإجمالية',
                mealStarted: 'بدأت وجبة',
                mealCompleted: 'اكتملت وجبة',
                hallStatusChanged: 'تغيرت حالة القاعة',
                mecca: 'مكة المكرمة',
                settingsText: 'ضبط الوجبات',
                modalTitle: '⚙️ ضبط الوجبات والقاعات',
                mealsSettingsTitle: '🍽️ إعدادات الوجبات',
                hallsSettingsTitle: '🏛️ إعدادات القاعات',
                saveBtn: '💾 حفظ التغييرات',
                resetBtn: '🔄 إعادة تعيين',
                cancelBtn: '❌ إلغاء',
                hallNumber: 'رقم القاعة',
                hallCapacity: 'سعة القاعة',
                hallCurrent: 'العدد الحالي',
                hallStatus: 'حالة القاعة',
                settingsSaved: 'تم حفظ الإعدادات بنجاح',
                settingsReset: 'تم إعادة تعيين الإعدادات'
            },
            en: {
                mainTitle: '🕋 Hajj Meals Management System',
                subtitle: 'Comprehensive management for pilgrims meals in the holy sites',
                languageToggle: 'العربية',
                filterAll: 'All',
                filterActive: 'Active',
                filterWaiting: 'Waiting',
                filterCompleted: 'Completed',
                filterPreparing: 'Preparing',
                mealsTitle: '🍽️ Meals Management',
                hallsTitle: '🏛️ Halls Status',
                statsTitle: '📊 Statistics',
                breakfast: 'Breakfast',
                lunch: 'Lunch',
                dinner: 'Dinner',
                time: 'Time',
                duration: 'Duration',
                minutes: 'minutes',
                hall: 'Hall',
                capacity: 'Capacity',
                current: 'Current',
                status: 'Status',
                active: 'Active',
                waiting: 'Waiting',
                completed: 'Completed',
                preparing: 'Preparing',
                totalHalls: 'Total Halls',
                activeHalls: 'Active Halls',
                servedMeals: 'Served Meals',
                totalCapacity: 'Total Capacity',
                mealStarted: 'Meal started',
                mealCompleted: 'Meal completed',
                hallStatusChanged: 'Hall status changed',
                mecca: 'Mecca',
                settingsText: 'Meals Settings',
                modalTitle: '⚙️ Meals & Halls Settings',
                mealsSettingsTitle: '🍽️ Meals Settings',
                hallsSettingsTitle: '🏛️ Halls Settings',
                saveBtn: '💾 Save Changes',
                resetBtn: '🔄 Reset',
                cancelBtn: '❌ Cancel',
                hallNumber: 'Hall Number',
                hallCapacity: 'Hall Capacity',
                hallCurrent: 'Current Count',
                hallStatus: 'Hall Status',
                settingsSaved: 'Settings saved successfully',
                settingsReset: 'Settings reset successfully'
            }
        };

        // Data structures
        let meals = [
            {
                id: 'breakfast',
                nameAr: 'الإفطار',
                nameEn: 'Breakfast',
                time: '07:00',
                duration: 120,
                active: false
            },
            {
                id: 'lunch',
                nameAr: 'الغداء',
                nameEn: 'Lunch',
                time: '13:00',
                duration: 150,
                active: true
            },
            {
                id: 'dinner',
                nameAr: 'العشاء',
                nameEn: 'Dinner',
                time: '19:00',
                duration: 120,
                active: false
            }
        ];

        let halls = [];
        let currentFilter = 'all';

        // Initialize halls data
        function initializeHalls() {
            const statuses = ['active', 'waiting', 'completed', 'preparing'];
            halls = [];

            for (let i = 1; i <= 12; i++) {
                halls.push({
                    id: i,
                    name: `${translations[currentLanguage].hall} ${i}`,
                    capacity: Math.floor(Math.random() * 200) + 100,
                    current: Math.floor(Math.random() * 150) + 50,
                    status: statuses[Math.floor(Math.random() * statuses.length)]
                });
            }
        }

        // Mecca time (UTC+3)
        function getMeccaTime() {
            const now = new Date();
            const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
            const meccaTime = new Date(utc + (3 * 3600000));
            return meccaTime;
        }

        // Update datetime display
        function updateDateTime() {
            const meccaTime = getMeccaTime();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };

            const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
            const timeString = meccaTime.toLocaleString(locale, options);

            document.getElementById('datetime-display').textContent =
                `${translations[currentLanguage].time}: ${timeString} (${translations[currentLanguage].mecca})`;
        }

        // Show notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.getElementById('notifications').appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Toggle language
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            document.documentElement.lang = currentLanguage;
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';

            updateUI();
            initializeHalls();
            renderMeals();
            renderHalls();
            renderStats();
        }

        // Update UI text
        function updateUI() {
            const t = translations[currentLanguage];

            document.getElementById('main-title').textContent = t.mainTitle;
            document.getElementById('subtitle').textContent = t.subtitle;
            document.getElementById('language-toggle').textContent = t.languageToggle;
            document.getElementById('settings-text').textContent = t.settingsText;
            document.getElementById('meals-title').textContent = t.mealsTitle;
            document.getElementById('halls-title').textContent = t.hallsTitle;
            document.getElementById('stats-title').textContent = t.statsTitle;

            // Modal texts
            document.getElementById('modal-title').textContent = t.modalTitle;
            document.getElementById('meals-settings-title').textContent = t.mealsSettingsTitle;
            document.getElementById('halls-settings-title').textContent = t.hallsSettingsTitle;
            document.getElementById('save-btn').textContent = t.saveBtn;
            document.getElementById('reset-btn').textContent = t.resetBtn;
            document.getElementById('cancel-btn').textContent = t.cancelBtn;

            updateFilterButtons();
        }

        // Update filter buttons with counts
        function updateFilterButtons() {
            const t = translations[currentLanguage];
            const counts = getHallCounts();

            document.getElementById('filter-all').innerHTML = `${t.filterAll} <span class="hall-count" id="count-all">${counts.all}</span>`;
            document.getElementById('filter-active').innerHTML = `${t.filterActive} <span class="hall-count" id="count-active">${counts.active}</span>`;
            document.getElementById('filter-waiting').innerHTML = `${t.filterWaiting} <span class="hall-count" id="count-waiting">${counts.waiting}</span>`;
            document.getElementById('filter-completed').innerHTML = `${t.filterCompleted} <span class="hall-count" id="count-completed">${counts.completed}</span>`;
            document.getElementById('filter-preparing').innerHTML = `${t.filterPreparing} <span class="hall-count" id="count-preparing">${counts.preparing}</span>`;
        }

        // Get hall counts by status
        function getHallCounts() {
            return {
                all: halls.length,
                active: halls.filter(h => h.status === 'active').length,
                waiting: halls.filter(h => h.status === 'waiting').length,
                completed: halls.filter(h => h.status === 'completed').length,
                preparing: halls.filter(h => h.status === 'preparing').length
            };
        }

        // Render meals
        function renderMeals() {
            const mealsGrid = document.getElementById('meals-grid');
            const t = translations[currentLanguage];

            mealsGrid.innerHTML = meals.map(meal => `
                <div class="meal-card ${meal.active ? 'active' : ''}" onclick="toggleMeal('${meal.id}')">
                    <h3>${currentLanguage === 'ar' ? meal.nameAr : meal.nameEn}</h3>
                    <div class="meal-time">
                        <strong>${t.time}:</strong>
                        <input type="time" value="${meal.time}" onchange="updateMealTime('${meal.id}', this.value)" onclick="event.stopPropagation()">
                    </div>
                    <div class="meal-time">
                        <strong>${t.duration}:</strong>
                        <input type="number" value="${meal.duration}" min="30" max="300" onchange="updateMealDuration('${meal.id}', this.value)" onclick="event.stopPropagation()">
                        ${t.minutes}
                    </div>
                    <div class="meal-time">
                        <strong>${t.status}:</strong> ${meal.active ? t.active : t.waiting}
                    </div>
                </div>
            `).join('');
        }

        // Toggle meal status
        function toggleMeal(mealId) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.active = !meal.active;
                const t = translations[currentLanguage];
                const mealName = currentLanguage === 'ar' ? meal.nameAr : meal.nameEn;

                showNotification(
                    `${meal.active ? t.mealStarted : t.mealCompleted} ${mealName}`,
                    meal.active ? 'success' : 'warning'
                );

                renderMeals();
                renderStats();
            }
        }

        // Update meal time
        function updateMealTime(mealId, newTime) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.time = newTime;
            }
        }

        // Update meal duration
        function updateMealDuration(mealId, newDuration) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.duration = parseInt(newDuration);
            }
        }

        // Filter halls
        function filterHalls(status) {
            currentFilter = status;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            renderHalls();
        }

        // Render halls
        function renderHalls() {
            const hallsGrid = document.getElementById('halls-grid');
            const t = translations[currentLanguage];

            let filteredHalls = halls;
            if (currentFilter !== 'all') {
                filteredHalls = halls.filter(hall => hall.status === currentFilter);
            }

            hallsGrid.innerHTML = filteredHalls.map(hall => `
                <div class="hall-card ${hall.status}" onclick="changeHallStatus(${hall.id})">
                    <h3>${t.hall} ${hall.id}</h3>
                    <p><strong>${t.capacity}:</strong> ${hall.capacity}</p>
                    <p><strong>${t.current}:</strong> ${hall.current}</p>
                    <p><strong>${t.status}:</strong> ${t[hall.status]}</p>
                    <div style="margin-top: 10px; background: rgba(255,255,255,0.2); border-radius: 10px; height: 8px;">
                        <div style="background: rgba(255,255,255,0.8); height: 100%; border-radius: 10px; width: ${(hall.current / hall.capacity) * 100}%;"></div>
                    </div>
                </div>
            `).join('');
        }

        // Change hall status
        function changeHallStatus(hallId) {
            const hall = halls.find(h => h.id === hallId);
            if (hall) {
                const statuses = ['active', 'waiting', 'completed', 'preparing'];
                const currentIndex = statuses.indexOf(hall.status);
                const nextIndex = (currentIndex + 1) % statuses.length;
                hall.status = statuses[nextIndex];

                const t = translations[currentLanguage];
                showNotification(`${t.hallStatusChanged} ${hall.id}: ${t[hall.status]}`);

                renderHalls();
                renderStats();
                updateFilterButtons();
            }
        }

        // Open settings modal
        function openSettingsModal() {
            document.getElementById('settingsModal').style.display = 'block';
            renderMealsSettings();
            renderHallsSettings();
        }

        // Close settings modal
        function closeSettingsModal() {
            document.getElementById('settingsModal').style.display = 'none';
        }

        // Render meals settings
        function renderMealsSettings() {
            const mealsSettings = document.getElementById('meals-settings');
            const t = translations[currentLanguage];

            mealsSettings.innerHTML = meals.map(meal => `
                <div class="form-group">
                    <label><strong>${currentLanguage === 'ar' ? meal.nameAr : meal.nameEn}</strong></label>
                    <div class="form-row">
                        <div class="form-group">
                            <label>${t.time}:</label>
                            <input type="time" value="${meal.time}" onchange="updateMealSettingTime('${meal.id}', this.value)">
                        </div>
                        <div class="form-group">
                            <label>${t.duration} (${t.minutes}):</label>
                            <input type="number" value="${meal.duration}" min="30" max="300" onchange="updateMealSettingDuration('${meal.id}', this.value)">
                        </div>
                        <div class="form-group">
                            <label>${t.status}:</label>
                            <select onchange="updateMealSettingStatus('${meal.id}', this.value)">
                                <option value="false" ${!meal.active ? 'selected' : ''}>${t.waiting}</option>
                                <option value="true" ${meal.active ? 'selected' : ''}>${t.active}</option>
                            </select>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Render halls settings
        function renderHallsSettings() {
            const hallsSettings = document.getElementById('halls-settings');
            const t = translations[currentLanguage];

            hallsSettings.innerHTML = halls.map(hall => `
                <div class="form-group">
                    <label><strong>${t.hall} ${hall.id}</strong></label>
                    <div class="form-row">
                        <div class="form-group">
                            <label>${t.hallCapacity}:</label>
                            <input type="number" value="${hall.capacity}" min="50" max="500" onchange="updateHallCapacity(${hall.id}, this.value)">
                        </div>
                        <div class="form-group">
                            <label>${t.hallCurrent}:</label>
                            <input type="number" value="${hall.current}" min="0" max="${hall.capacity}" onchange="updateHallCurrent(${hall.id}, this.value)">
                        </div>
                        <div class="form-group">
                            <label>${t.hallStatus}:</label>
                            <div class="hall-status-buttons">
                                <button class="status-btn active-status ${hall.status === 'active' ? 'selected' : ''}" onclick="updateHallStatus(${hall.id}, 'active')">${t.active}</button>
                                <button class="status-btn waiting-status ${hall.status === 'waiting' ? 'selected' : ''}" onclick="updateHallStatus(${hall.id}, 'waiting')">${t.waiting}</button>
                                <button class="status-btn completed-status ${hall.status === 'completed' ? 'selected' : ''}" onclick="updateHallStatus(${hall.id}, 'completed')">${t.completed}</button>
                                <button class="status-btn preparing-status ${hall.status === 'preparing' ? 'selected' : ''}" onclick="updateHallStatus(${hall.id}, 'preparing')">${t.preparing}</button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Update meal setting functions
        function updateMealSettingTime(mealId, newTime) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.time = newTime;
            }
        }

        function updateMealSettingDuration(mealId, newDuration) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.duration = parseInt(newDuration);
            }
        }

        function updateMealSettingStatus(mealId, newStatus) {
            const meal = meals.find(m => m.id === mealId);
            if (meal) {
                meal.active = newStatus === 'true';
            }
        }

        // Update hall setting functions
        function updateHallCapacity(hallId, newCapacity) {
            const hall = halls.find(h => h.id === hallId);
            if (hall) {
                hall.capacity = parseInt(newCapacity);
                // Ensure current doesn't exceed capacity
                if (hall.current > hall.capacity) {
                    hall.current = hall.capacity;
                }
            }
        }

        function updateHallCurrent(hallId, newCurrent) {
            const hall = halls.find(h => h.id === hallId);
            if (hall) {
                hall.current = Math.min(parseInt(newCurrent), hall.capacity);
            }
        }

        function updateHallStatus(hallId, newStatus) {
            const hall = halls.find(h => h.id === hallId);
            if (hall) {
                hall.status = newStatus;
                renderHallsSettings(); // Re-render to update button states
            }
        }

        // Save settings
        function saveSettings() {
            const t = translations[currentLanguage];
            showNotification(t.settingsSaved, 'success');
            renderMeals();
            renderHalls();
            renderStats();
            updateFilterButtons();
            closeSettingsModal();
        }

        // Reset settings
        function resetSettings() {
            const t = translations[currentLanguage];

            // Reset meals
            meals.forEach(meal => {
                if (meal.id === 'breakfast') {
                    meal.time = '07:00';
                    meal.duration = 120;
                    meal.active = false;
                } else if (meal.id === 'lunch') {
                    meal.time = '13:00';
                    meal.duration = 150;
                    meal.active = true;
                } else if (meal.id === 'dinner') {
                    meal.time = '19:00';
                    meal.duration = 120;
                    meal.active = false;
                }
            });

            // Reset halls
            initializeHalls();

            showNotification(t.settingsReset, 'warning');
            renderMealsSettings();
            renderHallsSettings();
        }

        // Render statistics
        function renderStats() {
            const statsGrid = document.getElementById('stats-grid');
            const t = translations[currentLanguage];

            const totalHalls = halls.length;
            const activeHalls = halls.filter(h => h.status === 'active').length;
            const servedMeals = meals.filter(m => m.active).length;
            const totalCapacity = halls.reduce((sum, hall) => sum + hall.capacity, 0);

            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${totalHalls}</div>
                    <div class="stat-label">${t.totalHalls}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${activeHalls}</div>
                    <div class="stat-label">${t.activeHalls}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${servedMeals}</div>
                    <div class="stat-label">${t.servedMeals}</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalCapacity.toLocaleString()}</div>
                    <div class="stat-label">${t.totalCapacity}</div>
                </div>
            `;
        }

        // Auto-update functions
        function autoUpdateHalls() {
            // Simulate real-time updates
            halls.forEach(hall => {
                if (Math.random() < 0.1) { // 10% chance of update
                    hall.current = Math.max(0, Math.min(hall.capacity,
                        hall.current + Math.floor(Math.random() * 21) - 10));
                }
            });

            if (currentFilter === 'all' || halls.some(h => h.status === currentFilter)) {
                renderHalls();
                renderStats();
                updateFilterButtons();
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('settingsModal');
            if (event.target === modal) {
                closeSettingsModal();
            }
        }

        // Initialize application
        function init() {
            initializeHalls();
            updateUI();
            renderMeals();
            renderHalls();
            renderStats();
            updateFilterButtons();
            updateDateTime();

            // Set up intervals
            setInterval(updateDateTime, 1000);
            setInterval(autoUpdateHalls, 5000);

            // Welcome notification
            setTimeout(() => {
                const t = translations[currentLanguage];
                showNotification('مرحباً بك في نظام إدارة وجبات الحج! 🕋');
            }, 1000);
        }

        // Start the application
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
