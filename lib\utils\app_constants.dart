class AppConstants {
  // معلومات التطبيق
  static const String appName = 'مهام الحاج اليومية';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق يساعد الحجاج على متابعة مهامهم اليومية خلال رحلة الحج المباركة';

  // الألوان الرئيسية
  static const primaryColorValue = 0xFF4CAF50; // أخضر
  static const secondaryColorValue = 0xFF2196F3; // أزرق
  static const accentColorValue = 0xFFFF9800; // برتقالي

  // أحجام النصوص
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;

  // المسافات
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // أحجام الأيقونات
  static const double smallIconSize = 16.0;
  static const double defaultIconSize = 24.0;
  static const double largeIconSize = 32.0;

  // نصائح وإرشادات
  static const List<String> generalTips = [
    'احتفظ برقم مفتي الحملة في هاتفك',
    'تابع التطبيق يومياً للحصول على التذكيرات',
    'لا تتردد في السؤال عند الحاجة',
    'أكثر من الدعاء والذكر',
    'اشرب الماء بكثرة',
    'احرص على النظافة الشخصية',
    'تعاون مع زملائك في الحملة',
  ];

  // أدعية مختارة
  static const List<String> selectedDuas = [
    'لبيك اللهم لبيك، لبيك لا شريك لك لبيك، إن الحمد والنعمة لك والملك، لا شريك لك',
    'اللهم تقبل مني حجي واجعله مبروراً وسعيي مشكوراً وذنبي مغفوراً',
    'ربنا آتنا في الدنيا حسنة وفي الآخرة حسنة وقنا عذاب النار',
    'اللهم أعني على ذكرك وشكرك وحسن عبادتك',
    'رب اغفر لي ولوالدي وللمؤمنين يوم يقوم الحساب',
  ];

  // معلومات الاتصال الطارئة
  static const Map<String, String> emergencyContacts = {
    'الهلال الأحمر': '997',
    'الدفاع المدني': '998',
    'الشرطة': '999',
    'الطوارئ العامة': '911',
  };

  // مواقيت الصلاة التقريبية (يمكن تحديثها حسب الموقع)
  static const Map<String, String> prayerTimes = {
    'الفجر': '04:30',
    'الشروق': '06:00',
    'الظهر': '12:15',
    'العصر': '15:45',
    'المغرب': '18:30',
    'العشاء': '20:00',
  };

  // رسائل التحفيز
  static const List<String> motivationalMessages = [
    'بارك الله فيكم وتقبل حجكم',
    'أنتم في أطهر بقاع الأرض',
    'اغتنموا هذه الأيام المباركة',
    'ادعوا لأنفسكم وأهليكم والأمة',
    'اصبروا واحتسبوا الأجر عند الله',
    'هذه رحلة العمر فاستمتعوا بها',
  ];

  // إعدادات التطبيق
  static const String prefsKeyFirstLaunch = 'first_launch';
  static const String prefsKeyCompletedTasks = 'completed_tasks';
  static const String prefsKeyNotificationsEnabled = 'notifications_enabled';
  static const String prefsKeySelectedLanguage = 'selected_language';

  // روابط مفيدة
  static const Map<String, String> usefulLinks = {
    'دليل الحج': 'https://www.haj.gov.sa',
    'تطبيق الحرمين': 'https://play.google.com/store/apps/details?id=sa.gov.haj.haramain',
    'موقع الحج والعمرة': 'https://www.haj.gov.sa/ar/Pages/default.aspx',
  };

  // رسائل النجاح
  static const Map<String, String> successMessages = {
    'task_completed': 'تم تسجيل إنجاز المهمة بنجاح',
    'data_saved': 'تم حفظ البيانات بنجاح',
    'settings_updated': 'تم تحديث الإعدادات بنجاح',
  };

  // رسائل الخطأ
  static const Map<String, String> errorMessages = {
    'network_error': 'تعذر الاتصال بالإنترنت',
    'data_load_error': 'حدث خطأ في تحميل البيانات',
    'save_error': 'حدث خطأ في حفظ البيانات',
    'phone_call_error': 'تعذر إجراء المكالمة',
  };
}
