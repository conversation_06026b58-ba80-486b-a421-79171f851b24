import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/hajj_task.dart';
import '../widgets/camp_map.dart';

class WelcomeScreen extends StatelessWidget {
  final CampInfo campInfo;

  const WelcomeScreen({super.key, required this.campInfo});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.green[400]!, Colors.green[600]!],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // الترحيب الرئيسي
                Container(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // شعار حملة الفرقان
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(60),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.mosque,
                          color: Colors.green,
                          size: 60,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // اسم التطبيق والحملة
                      const Text(
                        'المهام اليومية للحج',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      const Text(
                        'حملة الفرقان',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      const Text(
                        'تقبل الله حجكم وجعله مبروراً',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 20),

                      // النص الترحيبي
                      const Text(
                        'بسم الله الرحمن الرحيم',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 10),

                      const SizedBox(height: 20),

                      // بطاقة رقم المفتي
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.phone,
                                  color: Colors.green[600],
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'رقم مفتي الحملة',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green[700],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  campInfo.muftiNumber,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                ElevatedButton.icon(
                                  onPressed:
                                      () =>
                                          _makePhoneCall(campInfo.muftiNumber),
                                  icon: const Icon(Icons.call),
                                  label: const Text('اتصال'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green[600],
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'للاستفسارات والطوارئ على مدار الساعة',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // خارطة المخيم
                CampMapWidget(campInfo: campInfo),

                const SizedBox(height: 20),

                // رسالة ختامية
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue[600],
                        size: 24,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'نصائح مهمة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '• احتفظ برقم مفتي الحملة في هاتفك\n'
                        '• تابع التطبيق يومياً للحصول على التذكيرات\n'
                        '• لا تتردد في السؤال عند الحاجة\n'
                        '• نسأل الله أن يوفقكم لأداء مناسككم',
                        style: TextStyle(fontSize: 14, height: 1.5),
                        textAlign: TextAlign.right,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      await launchUrl(launchUri);
    } catch (e) {
      // Handle error - could show a snackbar or dialog
      debugPrint('Could not launch $launchUri: $e');
    }
  }
}
