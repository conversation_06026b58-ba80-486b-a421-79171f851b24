#!/bin/bash

echo "========================================"
echo "   بناء تطبيق المهام اليومية للحج"
echo "       للويب (iPhone/Android)"
echo "========================================"
echo

echo "[1/3] تنظيف المشروع..."
flutter clean

echo
echo "[2/3] تحديث التبعيات..."
flutter pub get

echo
echo "[3/3] بناء تطبيق الويب..."
flutter build web --release

echo
echo "========================================"
echo "          تم البناء بنجاح!"
echo "========================================"
echo
echo "الملفات موجودة في: build/web"
echo
echo "للاستخدام على الآيفون:"
echo "1. ارفع مجلد build/web على استضافة ويب"
echo "2. افتح الرابط في Safari على الآيفون"
echo "3. اضغط المشاركة ← إضافة للشاشة الرئيسية"
echo
echo "استضافات مجانية مقترحة:"
echo "- GitHub Pages"
echo "- Firebase Hosting"
echo "- Netlify"
echo "- Vercel"
echo
