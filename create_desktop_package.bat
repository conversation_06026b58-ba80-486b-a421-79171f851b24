@echo off
echo ========================================
echo      إنشاء حزمة التطبيق لسطح المكتب
echo    Creating Desktop App Package
echo ========================================
echo.

echo [1/6] إنشاء مجلد التطبيق على سطح المكتب...
echo Creating app folder on desktop...

set DESKTOP=%USERPROFILE%\Desktop
set APP_FOLDER=%DESKTOP%\تطبيق_الحج_1446_كامل
set WEB_FOLDER=%APP_FOLDER%\التطبيق_الويب

echo إنشاء المجلدات...
mkdir "%APP_FOLDER%" 2>nul
mkdir "%WEB_FOLDER%" 2>nul
mkdir "%APP_FOLDER%\الأدلة_والتعليمات" 2>nul
mkdir "%APP_FOLDER%\ملفات_APK" 2>nul

echo [2/6] نسخ ملفات التطبيق الويب...
echo Copying web app files...

xcopy "hajj-tasks-app-1446\*" "%WEB_FOLDER%\" /E /I /Y >nul 2>&1

echo [3/6] نسخ الأدلة والتعليمات...
echo Copying guides and instructions...

copy "دليل_التحميل_الشامل.md" "%APP_FOLDER%\الأدلة_والتعليمات\" >nul 2>&1
copy "دليل_الحصول_على_رابط_التطبيق.md" "%APP_FOLDER%\الأدلة_والتعليمات\" >nul 2>&1
copy "دليل_تحديد_الملفات_الصحيحة.md" "%APP_FOLDER%\الأدلة_والتعليمات\" >nul 2>&1
copy "GitHub_Upload_Instructions.md" "%APP_FOLDER%\الأدلة_والتعليمات\" >nul 2>&1
copy "create_apk_alternative.bat" "%APP_FOLDER%\ملفات_APK\" >nul 2>&1

echo [4/6] إنشاء ملف README...
echo Creating README file...

echo # 🕋 تطبيق المهام اليومية للحج 1446 هـ > "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ## 🎯 **محتويات الحزمة:** >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### 📱 **التطبيق الويب:** >> "%APP_FOLDER%\README.md"
echo - **المجلد:** `التطبيق_الويب/` >> "%APP_FOLDER%\README.md"
echo - **الملف الرئيسي:** `index.html` >> "%APP_FOLDER%\README.md"
echo - **للتشغيل:** انقر مرتين على `index.html` >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### 🌐 **الرابط المباشر:** >> "%APP_FOLDER%\README.md"
echo ```>> "%APP_FOLDER%\README.md"
echo https://alfahed-151.github.io/hajj-tasks-app-1446/ >> "%APP_FOLDER%\README.md"
echo ```>> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### 📚 **الأدلة والتعليمات:** >> "%APP_FOLDER%\README.md"
echo - دليل التحميل الشامل >> "%APP_FOLDER%\README.md"
echo - دليل الحصول على رابط التطبيق >> "%APP_FOLDER%\README.md"
echo - تعليمات رفع GitHub >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### 📱 **ملفات APK:** >> "%APP_FOLDER%\README.md"
echo - أدوات إنشاء APK للأندرويد >> "%APP_FOLDER%\README.md"
echo - تعليمات استخدام PWABuilder >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ## 🚀 **طرق الاستخدام:** >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### **1. التشغيل المحلي:** >> "%APP_FOLDER%\README.md"
echo - انقر مرتين على `التطبيق_الويب/index.html` >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### **2. الرابط المباشر:** >> "%APP_FOLDER%\README.md"
echo - استخدم الرابط أعلاه >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ### **3. إنشاء APK:** >> "%APP_FOLDER%\README.md"
echo - استخدم الملفات في مجلد `ملفات_APK/` >> "%APP_FOLDER%\README.md"
echo. >> "%APP_FOLDER%\README.md"
echo ## ✨ **المميزات:** >> "%APP_FOLDER%\README.md"
echo - ✅ واجهة عربية كاملة >> "%APP_FOLDER%\README.md"
echo - ✅ مهام يومية للحج (أيام 8-13) >> "%APP_FOLDER%\README.md"
echo - ✅ خارطة المخيم قابلة للتعديل >> "%APP_FOLDER%\README.md"
echo - ✅ أوقات الصلاة >> "%APP_FOLDER%\README.md"
echo - ✅ رقم المفتي قابل للتخصيص >> "%APP_FOLDER%\README.md"
echo - ✅ الأسئلة الشائعة >> "%APP_FOLDER%\README.md"
echo - ✅ يعمل على جميع الأجهزة >> "%APP_FOLDER%\README.md"

echo [5/6] إنشاء ملف تشغيل سريع...
echo Creating quick launch file...

echo @echo off > "%APP_FOLDER%\تشغيل_التطبيق.bat"
echo echo تشغيل تطبيق الحج... >> "%APP_FOLDER%\تشغيل_التطبيق.bat"
echo start "" "التطبيق_الويب\index.html" >> "%APP_FOLDER%\تشغيل_التطبيق.bat"

echo [6/6] ضغط المجلد...
echo Compressing folder...

powershell -command "Compress-Archive -Path '%APP_FOLDER%' -DestinationPath '%DESKTOP%\تطبيق_الحج_1446_كامل.zip' -Force"

echo.
echo ========================================
echo        تم إنشاء الحزمة بنجاح! 🎉
echo        Package Created Successfully! 🎉
echo ========================================
echo.
echo 📁 مكان المجلد: %APP_FOLDER%
echo 📦 مكان الملف المضغوط: %DESKTOP%\تطبيق_الحج_1446_كامل.zip
echo.
echo 🚀 يمكنك الآن:
echo 1. فتح المجلد من سطح المكتب
echo 2. تشغيل التطبيق بالنقر على "تشغيل_التطبيق.bat"
echo 3. مشاركة الملف المضغوط
echo 4. رفع التطبيق على أي استضافة
echo.

echo فتح المجلد الآن...
echo Opening folder now...
explorer "%APP_FOLDER%"

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
