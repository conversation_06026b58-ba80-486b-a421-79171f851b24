@echo off
echo ========================================
echo      بناء تطبيق الحج APK للأندرويد
echo    Building Hajj Tasks APK for Android
echo ========================================
echo.

echo [1/5] تنظيف المشروع...
echo Cleaning project...
flutter clean
echo.

echo [2/5] تحديث التبعيات...
echo Getting dependencies...
flutter pub get
echo.

echo [3/5] بناء التطبيق للويب...
echo Building web version...
flutter build web --release
echo.

echo [4/5] بناء ملف APK...
echo Building APK file...
flutter build apk --release --no-shrink
echo.

echo [5/5] التحقق من النتيجة...
echo Checking result...
if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo.
    echo ========================================
    echo        تم بناء APK بنجاح! 🎉
    echo        APK Built Successfully! 🎉
    echo ========================================
    echo.
    echo 📱 مكان الملف:
    echo File location: build\app\outputs\flutter-apk\app-release.apk
    echo.
    echo 📊 معلومات الملف:
    dir "build\app\outputs\flutter-apk\app-release.apk"
    echo.
    echo 🚀 يمكنك الآن:
    echo 1. نسخ الملف إلى الهاتف
    echo 2. تثبيته على أجهزة الأندرويد
    echo 3. مشاركته مع الحجاج
    echo.
) else (
    echo.
    echo ========================================
    echo         فشل في بناء APK ❌
    echo         APK Build Failed ❌
    echo ========================================
    echo.
    echo 💡 الحلول المقترحة:
    echo 1. تأكد من تثبيت Android SDK
    echo 2. تأكد من تثبيت Flutter بشكل صحيح
    echo 3. جرب الأمر: flutter doctor
    echo 4. تأكد من اتصال الإنترنت
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
