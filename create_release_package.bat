@echo off
echo ========================================
echo     إنشاء حزمة التطبيق للتحميل
echo    تطبيق المهام اليومية للحج
echo        حملة الفرقان
echo ========================================
echo.

set RELEASE_DIR=hajj_app_release
set WEB_DIR=%RELEASE_DIR%\web_version
set DOCS_DIR=%RELEASE_DIR%\documentation

echo [1/6] إنشاء مجلدات الحزمة...
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR%
mkdir %RELEASE_DIR%
mkdir %WEB_DIR%
mkdir %DOCS_DIR%

echo [2/6] نسخ تطبيق الويب...
xcopy "build\web\*" "%WEB_DIR%\" /E /I /Y

echo [3/6] نسخ الوثائق والأدلة...
copy "README.md" "%DOCS_DIR%\" 2>nul
copy "IOS_DEPLOYMENT_GUIDE.md" "%DOCS_DIR%\" 2>nul
copy "QUICK_IPHONE_SETUP.md" "%DOCS_DIR%\" 2>nul
copy "LOGO_INTEGRATION_GUIDE.md" "%DOCS_DIR%\" 2>nul
copy "CAMP_MAP_GUIDE.md" "%DOCS_DIR%\" 2>nul

echo [4/6] إنشاء ملف التعليمات الرئيسي...
(
echo # 📱 تطبيق المهام اليومية للحج - حملة الفرقان
echo.
echo ## 🎉 مرحباً بك في التطبيق الشامل للحج
echo.
echo ### 📁 محتويات الحزمة:
echo - **web_version/** - تطبيق الويب الجاهز للآيفون
echo - **documentation/** - جميع الأدلة والوثائق
echo.
echo ### 🚀 للاستخدام السريع على الآيفون:
echo 1. ارفع مجلد web_version على استضافة مجانية
echo 2. افتح الرابط في Safari على الآيفون
echo 3. أضف للشاشة الرئيسية
echo.
echo ### 📚 الأدلة المتاحة:
echo - **QUICK_IPHONE_SETUP.md** - دليل سريع للآيفون
echo - **IOS_DEPLOYMENT_GUIDE.md** - دليل شامل للنشر
echo - **LOGO_INTEGRATION_GUIDE.md** - دليل إضافة الشعار
echo - **CAMP_MAP_GUIDE.md** - دليل تخصيص خارطة المخيم
echo.
echo ### ✨ ميزات التطبيق:
echo - 📅 8 مهام يومية مفصلة للحج
echo - 🗺️ خارطة مخيم قابلة للتعديل
echo - 📞 رقم مفتي قابل للتغيير
echo - 🎨 واجهة عربية احترافية
echo - 📱 يعمل على الآيفون كتطبيق حقيقي
echo.
echo ### 🌐 استضافات مجانية مُوصى بها:
echo - **Netlify.com** - اسحب وأفلت
echo - **GitHub Pages** - مجاني للأبد
echo - **Firebase Hosting** - من Google
echo - **Vercel** - سريع جداً
echo.
echo **تقبل الله حجكم وجعله مبروراً** 🕋
) > "%RELEASE_DIR%\اقرأني_أولاً.md"

echo [5/6] إنشاء ملف تعليمات سريع...
(
echo # 🚀 تعليمات سريعة - 3 خطوات فقط
echo.
echo ## للاستخدام على الآيفون:
echo.
echo ### الخطوة 1: رفع التطبيق
echo - اذهب إلى netlify.com
echo - اسحب مجلد "web_version" إلى الموقع
echo - احصل على رابط فوري
echo.
echo ### الخطوة 2: فتح على الآيفون
echo - افتح Safari في الآيفون
echo - اذهب للرابط الذي حصلت عليه
echo.
echo ### الخطوة 3: إضافة للشاشة الرئيسية
echo - اضغط أيقونة المشاركة 📤
echo - اختر "إضافة إلى الشاشة الرئيسية"
echo - اكتب الاسم: "المهام اليومية للحج"
echo.
echo ## النتيجة:
echo ✅ تطبيق كامل في الشاشة الرئيسية
echo ✅ يعمل مثل تطبيق حقيقي
echo ✅ جميع الميزات متاحة
echo.
echo **مبروك! 🎉**
) > "%RELEASE_DIR%\تعليمات_سريعة.md"

echo [6/6] إنشاء ملف معلومات الإصدار...
(
echo # 📋 معلومات الإصدار
echo.
echo **اسم التطبيق:** المهام اليومية للحج - حملة الفرقان
echo **الإصدار:** 1.0.0
echo **تاريخ البناء:** %date% %time%
echo **نوع البناء:** Web Release
echo.
echo ## 🎯 الميزات المتضمنة:
echo - ✅ 8 مهام يومية شاملة للحج
echo - ✅ خارطة مخيم قابلة للتعديل بالكامل
echo - ✅ رقم مفتي قابل للتغيير والحفظ
echo - ✅ واجهة عربية متكاملة
echo - ✅ تصميم متجاوب مع جميع الشاشات
echo - ✅ عمل بدون إنترنت بعد التحميل
echo - ✅ حفظ تلقائي لجميع الإعدادات
echo.
echo ## 📱 التوافق:
echo - ✅ آيفون ^(iOS 11+^)
echo - ✅ أندرويد ^(Android 5.0+^)
echo - ✅ جميع المتصفحات الحديثة
echo.
echo ## 🔧 التقنيات المستخدمة:
echo - Flutter Web
echo - Progressive Web App ^(PWA^)
echo - Local Storage للحفظ
echo - Responsive Design
echo.
echo **تم البناء بواسطة Flutter** 💙
) > "%RELEASE_DIR%\معلومات_الإصدار.md"

echo.
echo ========================================
echo        تم إنشاء الحزمة بنجاح!
echo ========================================
echo.
echo 📁 المجلد: %RELEASE_DIR%
echo 📦 يحتوي على:
echo   - web_version/ (التطبيق الجاهز)
echo   - documentation/ (جميع الأدلة)
echo   - ملفات التعليمات والمعلومات
echo.
echo 🚀 للاستخدام:
echo 1. ارفع مجلد web_version على استضافة ويب
echo 2. افتح الرابط في Safari على الآيفون
echo 3. أضف للشاشة الرئيسية
echo.
echo 📚 راجع ملف "اقرأني_أولاً.md" للتفاصيل
echo.
pause
