# مجلد الشعارات

هذا المجلد مخصص لشعارات التطبيق.

## الملفات المطلوبة:

### شعار حملة الفرقان ⭐
- **forgan_logo.png** - الشعار الرسمي لحملة الفرقان (مطلوب)

### الشعار الرئيسي
- **app_logo.png** - الشعار الرئيسي للتطبيق (512x512 بكسل)
- **app_logo_small.png** - نسخة صغيرة للشعار (128x128 بكسل)
- **app_logo_white.png** - نسخة بيضاء للخلفيات الداكنة

### شعارات إضافية
- **splash_logo.png** - شعار شاشة البداية (256x256 بكسل)
- **header_logo.png** - شعار رأس الصفحة (200x80 بكسل)

## المواصفات المطلوبة:

### الحجم والجودة
- **الصيغة**: PNG مع خلفية شفافة
- **الجودة**: عالية الدقة (300 DPI أو أكثر)
- **الألوان**: متوافقة مع ألوان التطبيق (أخضر إسلامي)

### التصميم
- **الطابع**: إسلامي مناسب للحج
- **العناصر المقترحة**: 
  - الكعبة المشرفة
  - الهلال الإسلامي
  - خط عربي جميل
  - ألوان هادئة ومريحة

## كيفية إضافة شعار حملة الفرقان:

### 🎯 خطوات مهمة:
1. **احفظ الصورة المرفقة باسم:** `forgan_logo.png`
2. **ضع الملف في هذا المجلد:** `assets/logos/`
3. **أعد تشغيل التطبيق:** `flutter run`
4. **ستظهر الصورة في:**
   - شاشة الترحيب (الشعار الرئيسي)
   - شاشة الإعدادات (قسم عن التطبيق)

### 🛡️ في حالة عدم وجود الصورة:
سيعرض التطبيق تصميماً احتياطياً جميلاً مع:
- أيقونة مسجد بيضاء
- خلفية بنية أنيقة
- نص "الفرقان"

## كيفية الاستخدام العام:

1. ضع ملف الشعار في هذا المجلد
2. تأكد من تسمية الملف بـ `app_logo.png` أو `forgan_logo.png`
3. قم بتشغيل `flutter pub get`
4. سيظهر الشعار تلقائياً في التطبيق

## ملاحظات:
- يفضل أن يكون الشعار بسيطاً وواضحاً
- تجنب النصوص الصغيرة التي قد لا تظهر بوضوح
- اختبر الشعار على خلفيات مختلفة
