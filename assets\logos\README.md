# مجلد الشعارات

هذا المجلد مخصص لشعارات التطبيق.

## الملفات المطلوبة:

### الشعار الرئيسي
- **app_logo.png** - الشعار الرئيسي للتطبيق (512x512 بكسل)
- **app_logo_small.png** - نسخة صغيرة للشعار (128x128 بكسل)
- **app_logo_white.png** - نسخة بيضاء للخلفيات الداكنة

### شعارات إضافية
- **splash_logo.png** - شعار شاشة البداية (256x256 بكسل)
- **header_logo.png** - شعار رأس الصفحة (200x80 بكسل)

## المواصفات المطلوبة:

### الحجم والجودة
- **الصيغة**: PNG مع خلفية شفافة
- **الجودة**: عالية الدقة (300 DPI أو أكثر)
- **الألوان**: متوافقة مع ألوان التطبيق (أخضر إسلامي)

### التصميم
- **الطابع**: إسلامي مناسب للحج
- **العناصر المقترحة**: 
  - الكعبة المشرفة
  - الهلال الإسلامي
  - خط عربي جميل
  - ألوان هادئة ومريحة

## كيفية الاستخدام:

1. ضع ملف الشعار في هذا المجلد
2. تأكد من تسمية الملف بـ `app_logo.png`
3. قم بتشغيل `flutter pub get`
4. سيظهر الشعار تلقائياً في التطبيق

## ملاحظات:
- يفضل أن يكون الشعار بسيطاً وواضحاً
- تجنب النصوص الصغيرة التي قد لا تظهر بوضوح
- اختبر الشعار على خلفيات مختلفة
