@echo off
echo ========================================
echo      حل بديل لإنشاء تطبيق APK
echo    Alternative APK Creation Solution
echo ========================================
echo.

echo المشكلة: تعارض مع الأرقام العربية في Gradle
echo Problem: Arabic numerals conflict in Gradle
echo.

echo 💡 الحلول المتاحة:
echo Available Solutions:
echo.

echo 1️⃣ استخدام التطبيق الحالي (PWA):
echo    - يعمل على جميع الأجهزة
echo    - رابط: https://alfahed-151.github.io/hajj-tasks-app-1446/
echo    - يمكن إضافته للشاشة الرئيسية
echo    - يعمل مثل تطبيق عادي
echo.

echo 2️⃣ تحويل PWA إلى APK باستخدام أدوات أونلاين:
echo    أ) PWABuilder من Microsoft:
echo       - اذهب إلى: https://www.pwabuilder.com/
echo       - أدخل الرابط: https://alfahed-151.github.io/hajj-tasks-app-1446/
echo       - اختر Android
echo       - حمّل APK
echo.
echo    ب) Bubblewrap من Google:
echo       - أداة Google الرسمية لتحويل PWA إلى APK
echo       - npm install -g @bubblewrap/cli
echo       - bubblewrap init --manifest https://alfahed-151.github.io/hajj-tasks-app-1446/manifest.json
echo.

echo 3️⃣ استخدام Android Studio مباشرة:
echo    - افتح Android Studio
echo    - File > Open > اختر مجلد android
echo    - Build > Generate Signed Bundle/APK
echo    - اختر APK
echo    - اتبع الخطوات
echo.

echo 4️⃣ إصلاح مشكلة Gradle:
echo    - تغيير إعدادات النظام إلى الإنجليزية مؤقتاً
echo    - Control Panel > Region > Administrative > Change system locale
echo    - اختر English (United States)
echo    - أعد تشغيل الكمبيوتر
echo    - جرب flutter build apk مرة أخرى
echo.

echo 🎯 الحل الأسرع والأسهل:
echo    استخدم PWABuilder:
echo    1. اذهب إلى: https://www.pwabuilder.com/
echo    2. أدخل: https://alfahed-151.github.io/hajj-tasks-app-1446/
echo    3. اضغط Start
echo    4. اختر Android
echo    5. حمّل APK
echo.

echo ========================================
echo         معلومات التطبيق الحالي
echo         Current App Information
echo ========================================
echo.
echo 🌐 رابط التطبيق: https://alfahed-151.github.io/hajj-tasks-app-1446/
echo 📱 يعمل على: آيفون، أندرويد، كمبيوتر
echo ⚡ سرعة: ممتازة بعد التحميل الأول
echo 💾 تخزين: يعمل بدون إنترنت بعد التحميل
echo 🔄 تحديثات: تلقائية
echo.

pause
