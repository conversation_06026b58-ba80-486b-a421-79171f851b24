@echo off
echo ========================================
echo     رفع ومشاركة تطبيق الحج
echo    تطبيق المهام اليومية للحج
echo        حملة الفرقان
echo ========================================
echo.

set ZIP_FILE=تطبيق_المهام_اليومية_للحج_v1.0.zip

echo [1/2] التحقق من وجود الملف...
if not exist "%ZIP_FILE%" (
    echo ❌ الملف المضغوط غير موجود!
    echo.
    echo 💡 تأكد من وجود الملف: %ZIP_FILE%
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على الملف المضغوط
echo.

echo 📊 معلومات الملف:
for %%A in ("%ZIP_FILE%") do (
    echo    الاسم: %%~nxA
    echo    الحجم: %%~zA bytes
    echo    التاريخ: %%~tA
)
echo.

echo [2/2] اختر طريقة المشاركة:
echo.
echo 1. نسخ إلى سطح المكتب
echo 2. نسخ إلى مجلد Downloads
echo 3. فتح مجلد الملف الحالي
echo 4. عرض تعليمات الرفع السحابي
echo 5. إلغاء
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto copy_desktop
if "%choice%"=="2" goto copy_downloads
if "%choice%"=="3" goto open_folder
if "%choice%"=="4" goto cloud_instructions
if "%choice%"=="5" goto end
goto invalid_choice

:copy_desktop
echo.
echo 📋 نسخ إلى سطح المكتب...
copy "%ZIP_FILE%" "%USERPROFILE%\Desktop\" >nul
if errorlevel 1 (
    echo ❌ فشل في النسخ
) else (
    echo ✅ تم نسخ الملف إلى سطح المكتب بنجاح!
    echo.
    echo 📁 المسار: %USERPROFILE%\Desktop\%ZIP_FILE%
)
goto end

:copy_downloads
echo.
echo 📥 نسخ إلى مجلد Downloads...
copy "%ZIP_FILE%" "%USERPROFILE%\Downloads\" >nul
if errorlevel 1 (
    echo ❌ فشل في النسخ
) else (
    echo ✅ تم نسخ الملف إلى مجلد Downloads بنجاح!
    echo.
    echo 📁 المسار: %USERPROFILE%\Downloads\%ZIP_FILE%
)
goto end

:open_folder
echo.
echo 📂 فتح مجلد الملف...
explorer .
echo ✅ تم فتح المجلد
goto end

:cloud_instructions
echo.
echo ☁️ تعليمات الرفع السحابي:
echo.
echo 🌟 الطرق المُوصى بها:
echo.
echo 1️⃣ Google Drive (15 جيجا مجاني):
echo    • اذهب إلى drive.google.com
echo    • اضغط "جديد" ← "رفع ملف"
echo    • اختر الملف المضغوط
echo    • اضغط "مشاركة" ← "أي شخص لديه الرابط"
echo    • انسخ الرابط وشاركه
echo.
echo 2️⃣ WeTransfer (2 جيجا، بدون تسجيل):
echo    • اذهب إلى wetransfer.com
echo    • اضغط "أضف ملفاتك"
echo    • اختر الملف المضغوط
echo    • احصل على رابط أو أرسل لإيميلات
echo.
echo 3️⃣ Dropbox (2 جيجا مجاني):
echo    • اذهب إلى dropbox.com
echo    • اسحب الملف إلى الموقع
echo    • اضغط "مشاركة"
echo    • انسخ الرابط
echo.
echo 💡 نصائح للمشاركة:
echo    • استخدم رابط مختصر للسهولة
echo    • أضف تعليمات للحجاج
echo    • تأكد من أن الرابط يعمل قبل المشاركة
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح
goto end

:end
echo.
echo 📱 رسالة للحجاج (يمكنك نسخها):
echo ========================================
echo 🕋 السلام عليكم ورحمة الله وبركاته
echo.
echo 📱 تطبيق المهام اليومية للحج - حملة الفرقان
echo.
echo 🎯 يحتوي على:
echo • 8 مهام يومية مفصلة للحج
echo • خارطة مخيم قابلة للتعديل  
echo • رقم مفتي الحملة
echo • واجهة عربية كاملة
echo.
echo 📥 للتحميل: [ضع الرابط هنا]
echo.
echo 📋 التعليمات:
echo 1. حمّل الملف المضغوط
echo 2. استخرج الملفات
echo 3. انقر على "تشغيل_التطبيق.html"
echo 4. اختر "تشغيل التطبيق مباشرة"
echo.
echo 📱 للآيفون: ارفع مجلد web_version على 
echo netlify.com وأضف الرابط للشاشة الرئيسية
echo.
echo تقبل الله حجكم وجعله مبروراً 🤲
echo ========================================
echo.
pause
