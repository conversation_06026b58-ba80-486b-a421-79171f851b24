import '../models/hajj_task.dart';

class HajjTasksData {
  static final CampInfo campInfo = CampInfo(
    muftiNumber: '+966 50 123 4567',
    campName: 'مخيم الحج المبارك',
    hallNumbers: {
      'الصالة الأولى': 'A1',
      'الصالة الثانية': 'A2',
      'الصالة الثالثة': 'B1',
      'الصالة الرابعة': 'B2',
    },
    serviceLocations: {
      'المطعم': 'الطابق الأرضي - الجناح الشرقي',
      'المسجد': 'الطابق الأرضي - الوسط',
      'العيادة الطبية': 'الطابق الأول - الجناح الغربي',
      'مكتب الاستعلامات': 'المدخل الرئيسي',
      'دورات المياه': 'جميع الطوابق',
    },
  );

  static List<HajjTask> getTasks() {
    final now = DateTime.now();
    final hajjStartDate = DateTime(now.year, 6, 15); // تاريخ تقريبي للحج

    return [
      // 1. الصفحة الترحيبية
      HajjTask(
        id: 'welcome',
        title: 'مرحباً بكم في رحلة الحج المباركة',
        description: 'معلومات المخيم ورقم المفتي',
        scheduledDate: hajjStartDate,
        timeOfDay: 'morning',
        content: [
          'أهلاً وسهلاً بكم في مخيم الحج المبارك',
          'رقم مفتي الحملة: ${campInfo.muftiNumber}',
          'للاستفسارات والطوارئ، يرجى الاتصال على الرقم أعلاه',
          'نسأل الله أن يتقبل منكم حجكم ويجعله مبروراً',
        ],
        type: TaskType.welcome,
      ),

      // 2. مساء السابع - يوم التروية
      HajjTask(
        id: 'day7_evening',
        title: 'أعمال يوم التروية',
        description: 'مساء السابع من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 7)),
        timeOfDay: 'evening',
        content: [
          'غداً يوم التروية (8 ذي الحجة)',
          'الإحرام للحج من المخيم',
          'التلبية: "لبيك اللهم حجاً"',
          'التوجه إلى منى بعد صلاة الفجر',
          'أداء الصلوات الخمس في منى',
          'المبيت في منى ليلة عرفة',
          'الإكثار من الذكر والدعاء',
        ],
        type: TaskType.ritual,
      ),

      // 3. مساء الثامن - الاستعداد لعرفة
      HajjTask(
        id: 'day8_evening',
        title: 'الاستعداد ليوم عرفة',
        description: 'مساء الثامن من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 8)),
        timeOfDay: 'evening',
        content: [
          'غداً يوم عرفة - اليوم الأعظم',
          'استعدوا لأعظم أيام الحج',
          'أكثروا من الدعاء والاستغفار',
          'تذكروا أن دعاء يوم عرفة أفضل الدعاء',
          'اشربوا الماء بكثرة واستعدوا للوقوف',
          'نوموا مبكراً للاستيقاظ نشطين',
          'تأكدوا من أخذ المظلة والماء',
        ],
        type: TaskType.ritual,
      ),

      // 4. صباح التاسع - يوم عرفة
      HajjTask(
        id: 'day9_morning',
        title: 'يوم عرفة المبارك',
        description: 'صباح التاسع من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 9)),
        timeOfDay: 'morning',
        content: [
          'اللهم أعن حجاجك على أداء مناسكهم',
          'اللهم تقبل منهم حجهم واجعله مبروراً',
          'اليوم يوم الوقوف بعرفة من بعد الزوال حتى المغرب',
          'أكثروا من الدعاء والتلبية والتكبير',
          'تذكروا أعمال غد (يوم النحر):',
          '- رمي جمرة العقبة الكبرى',
          '- النحر أو الذبح',
          '- الحلق أو التقصير',
          '- طواف الإفاضة',
        ],
        type: TaskType.prayer,
      ),

      // 5. صباح العاشر - يوم النحر
      HajjTask(
        id: 'day10_morning',
        title: 'عيد الأضحى المبارك',
        description: 'صباح العاشر من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 10)),
        timeOfDay: 'morning',
        content: [
          'عيد مبارك وتقبل الله حجكم',
          'اليوم يوم النحر - أعظم أيام السنة',
          'أعمال اليوم بالترتيب:',
          '1. رمي جمرة العقبة الكبرى',
          '2. النحر أو الذبح',
          '3. الحلق أو التقصير',
          '4. طواف الإفاضة',
          'بعد الحلق يحل لكم كل شيء إلا النساء',
          'ندعوكم لحضور حفل المعايدة في المخيم',
          'الموعد: بعد صلاة العصر في الصالة الكبرى',
        ],
        type: TaskType.celebration,
      ),

      // 6. مساء العاشر - أعمال الحادي عشر
      HajjTask(
        id: 'day10_evening',
        title: 'أعمال أيام التشريق',
        description: 'مساء العاشر من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 10)),
        timeOfDay: 'evening',
        content: [
          'غداً أول أيام التشريق (11 ذي الحجة)',
          'أعمال الغد:',
          '- رمي الجمرات الثلاث بعد الزوال',
          '- البدء بالصغرى ثم الوسطى ثم الكبرى',
          '- 7 حصيات لكل جمرة',
          '- الدعاء بعد الصغرى والوسطى',
          'المبيت في منى واجب',
          'يجوز التعجل في يومين لمن أراد',
        ],
        faqs: [
          'س: متى وقت رمي الجمرات؟\nج: من بعد الزوال حتى الفجر',
          'س: ماذا لو نسيت رمي جمرة؟\nج: ارمها عند التذكر وعليك دم',
          'س: هل يجوز الرمي قبل الزوال؟\nج: لا، إلا للضرورة',
        ],
        type: TaskType.faq,
      ),

      // 7. مساء الحادي عشر - أعمال الثاني عشر
      HajjTask(
        id: 'day11_evening',
        title: 'اليوم الثاني من التشريق',
        description: 'مساء الحادي عشر من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 11)),
        timeOfDay: 'evening',
        content: [
          'غداً اليوم الثاني من التشريق (12 ذي الحجة)',
          'أعمال الغد:',
          '- رمي الجمرات الثلاث بعد الزوال',
          '- نفس ترتيب الأمس',
          '- يجوز التعجل والخروج من منى قبل المغرب',
          '- من أراد التأخير فليبت ليلة 13',
          'للمتعجلين: طواف الوداع قبل السفر',
          'موضع الوقوف للدعاء: بين الصفا والمروة',
        ],
        images: [
          'assets/images/dua_position.jpg',
          'assets/images/jamarat_map.jpg',
        ],
        faqs: [
          'س: ما حكم طواف الوداع؟\nج: واجب على كل حاج إلا الحائض',
          'س: متى آخر وقت للتعجل؟\nج: قبل غروب شمس يوم 12',
          'س: هل يجوز ترك المبيت في منى؟\nج: لا، إلا لعذر وعليه دم',
        ],
        type: TaskType.faq,
      ),

      // 8. صباح الثاني عشر - حفل الختام
      HajjTask(
        id: 'day12_morning',
        title: 'حفل ختام الحج',
        description: 'صباح الثاني عشر من ذي الحجة',
        scheduledDate: hajjStartDate.add(const Duration(days: 12)),
        timeOfDay: 'morning',
        content: [
          'تقبل الله حجكم وجعله مبروراً',
          'ندعوكم لحضور حفل ختام رحلة الحج',
          'الموعد: بعد صلاة الفجر',
          'المكان: الصالة الكبرى بالمخيم',
          'البرنامج:',
          '- كلمة مفتي الحملة',
          '- توزيع الشهادات التذكارية',
          '- الدعاء الجماعي',
          '- وجبة الإفطار الجماعية',
          'نسأل الله أن يجعل حجكم مبروراً وسعيكم مشكوراً',
        ],
        type: TaskType.farewell,
      ),
    ];
  }
}
