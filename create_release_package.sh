#!/bin/bash

echo "========================================"
echo "     إنشاء حزمة التطبيق للتحميل"
echo "    تطبيق المهام اليومية للحج"
echo "        حملة الفرقان"
echo "========================================"
echo

RELEASE_DIR="hajj_app_release"
WEB_DIR="$RELEASE_DIR/web_version"
DOCS_DIR="$RELEASE_DIR/documentation"

echo "[1/6] إنشاء مجلدات الحزمة..."
rm -rf "$RELEASE_DIR"
mkdir -p "$WEB_DIR"
mkdir -p "$DOCS_DIR"

echo "[2/6] نسخ تطبيق الويب..."
cp -r build/web/* "$WEB_DIR/"

echo "[3/6] نسخ الوثائق والأدلة..."
cp README.md "$DOCS_DIR/" 2>/dev/null || true
cp IOS_DEPLOYMENT_GUIDE.md "$DOCS_DIR/" 2>/dev/null || true
cp QUICK_IPHONE_SETUP.md "$DOCS_DIR/" 2>/dev/null || true
cp LOGO_INTEGRATION_GUIDE.md "$DOCS_DIR/" 2>/dev/null || true
cp CAMP_MAP_GUIDE.md "$DOCS_DIR/" 2>/dev/null || true

echo "[4/6] إنشاء ملف التعليمات الرئيسي..."
cat > "$RELEASE_DIR/اقرأني_أولاً.md" << 'EOF'
# 📱 تطبيق المهام اليومية للحج - حملة الفرقان

## 🎉 مرحباً بك في التطبيق الشامل للحج

### 📁 محتويات الحزمة:
- **web_version/** - تطبيق الويب الجاهز للآيفون
- **documentation/** - جميع الأدلة والوثائق

### 🚀 للاستخدام السريع على الآيفون:
1. ارفع مجلد web_version على استضافة مجانية
2. افتح الرابط في Safari على الآيفون
3. أضف للشاشة الرئيسية

### 📚 الأدلة المتاحة:
- **QUICK_IPHONE_SETUP.md** - دليل سريع للآيفون
- **IOS_DEPLOYMENT_GUIDE.md** - دليل شامل للنشر
- **LOGO_INTEGRATION_GUIDE.md** - دليل إضافة الشعار
- **CAMP_MAP_GUIDE.md** - دليل تخصيص خارطة المخيم

### ✨ ميزات التطبيق:
- 📅 8 مهام يومية مفصلة للحج
- 🗺️ خارطة مخيم قابلة للتعديل
- 📞 رقم مفتي قابل للتغيير
- 🎨 واجهة عربية احترافية
- 📱 يعمل على الآيفون كتطبيق حقيقي

### 🌐 استضافات مجانية مُوصى بها:
- **Netlify.com** - اسحب وأفلت
- **GitHub Pages** - مجاني للأبد
- **Firebase Hosting** - من Google
- **Vercel** - سريع جداً

**تقبل الله حجكم وجعله مبروراً** 🕋
EOF

echo "[5/6] إنشاء ملف تعليمات سريع..."
cat > "$RELEASE_DIR/تعليمات_سريعة.md" << 'EOF'
# 🚀 تعليمات سريعة - 3 خطوات فقط

## للاستخدام على الآيفون:

### الخطوة 1: رفع التطبيق
- اذهب إلى netlify.com
- اسحب مجلد "web_version" إلى الموقع
- احصل على رابط فوري

### الخطوة 2: فتح على الآيفون
- افتح Safari في الآيفون
- اذهب للرابط الذي حصلت عليه

### الخطوة 3: إضافة للشاشة الرئيسية
- اضغط أيقونة المشاركة 📤
- اختر "إضافة إلى الشاشة الرئيسية"
- اكتب الاسم: "المهام اليومية للحج"

## النتيجة:
✅ تطبيق كامل في الشاشة الرئيسية
✅ يعمل مثل تطبيق حقيقي
✅ جميع الميزات متاحة

**مبروك! 🎉**
EOF

echo "[6/6] إنشاء ملف معلومات الإصدار..."
cat > "$RELEASE_DIR/معلومات_الإصدار.md" << EOF
# 📋 معلومات الإصدار

**اسم التطبيق:** المهام اليومية للحج - حملة الفرقان
**الإصدار:** 1.0.0
**تاريخ البناء:** $(date)
**نوع البناء:** Web Release

## 🎯 الميزات المتضمنة:
- ✅ 8 مهام يومية شاملة للحج
- ✅ خارطة مخيم قابلة للتعديل بالكامل
- ✅ رقم مفتي قابل للتغيير والحفظ
- ✅ واجهة عربية متكاملة
- ✅ تصميم متجاوب مع جميع الشاشات
- ✅ عمل بدون إنترنت بعد التحميل
- ✅ حفظ تلقائي لجميع الإعدادات

## 📱 التوافق:
- ✅ آيفون (iOS 11+)
- ✅ أندرويد (Android 5.0+)
- ✅ جميع المتصفحات الحديثة

## 🔧 التقنيات المستخدمة:
- Flutter Web
- Progressive Web App (PWA)
- Local Storage للحفظ
- Responsive Design

**تم البناء بواسطة Flutter** 💙
EOF

echo
echo "========================================"
echo "        تم إنشاء الحزمة بنجاح!"
echo "========================================"
echo
echo "📁 المجلد: $RELEASE_DIR"
echo "📦 يحتوي على:"
echo "  - web_version/ (التطبيق الجاهز)"
echo "  - documentation/ (جميع الأدلة)"
echo "  - ملفات التعليمات والمعلومات"
echo
echo "🚀 للاستخدام:"
echo "1. ارفع مجلد web_version على استضافة ويب"
echo "2. افتح الرابط في Safari على الآيفون"
echo "3. أضف للشاشة الرئيسية"
echo
echo "📚 راجع ملف \"اقرأني_أولاً.md\" للتفاصيل"
echo
