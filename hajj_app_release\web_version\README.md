# 📱 تطبيق المهام اليومية للحج - حملة الفرقان

## 🎉 تطبيق ويب جاهز للاستخدام على الآيفون!

هذا المجلد يحتوي على تطبيق ويب كامل يمكن تشغيله على أي آيفون أو جهاز ذكي.

---

## 🚀 كيفية الاستخدام على الآيفون

### **الخطوة 1: رفع على الإنترنت**
ارفع محتويات هذا المجلد على أي استضافة ويب مجانية:
- **Netlify.com** (الأسهل - اسحب وأفلت)
- **GitHub Pages** (مجاني للأبد)
- **Firebase Hosting** (من Google)
- **Vercel** (سريع جداً)

### **الخطوة 2: فتح على الآيفون**
1. افتح **Safari** في الآيفون
2. اذهب لرابط التطبيق
3. اضغط **أيقونة المشاركة** 📤
4. اختر **"إضافة إلى الشاشة الرئيسية"**
5. اكتب الاسم: **"المهام اليومية للحج"**
6. اضغط **"إضافة"**

### **النتيجة:**
- ✅ أيقونة تطبيق في الشاشة الرئيسية
- ✅ يعمل مثل تطبيق حقيقي
- ✅ جميع الميزات متاحة
- ✅ يعمل بدون إنترنت بعد التحميل الأول

---

## 🎯 ميزات التطبيق

### **📅 المهام اليومية:**
- مهام الحج من اليوم 8-13 من ذي الحجة
- تذكيرات يومية مفصلة
- أدعية وأذكار مناسبة
- جدول زمني واضح

### **🗺️ خارطة المخيم:**
- أرقام الصالات قابلة للتعديل
- مواقع الخدمات قابلة للتخصيص
- حفظ تلقائي للتغييرات
- واجهة سهلة الاستخدام

### **📞 معلومات الاتصال:**
- رقم مفتي الحملة قابل للتغيير
- إمكانية الاتصال المباشر
- حفظ دائم للإعدادات

### **🎨 التصميم:**
- واجهة عربية كاملة
- ألوان إسلامية هادئة
- تصميم متجاوب مع جميع الشاشات
- سهولة في الاستخدام

---

## 📁 محتويات المجلد

- **index.html** - الصفحة الرئيسية
- **main.dart.js** - كود التطبيق
- **assets/** - الصور والملفات
- **icons/** - أيقونات التطبيق
- **manifest.json** - إعدادات تطبيق الويب

---

## 🔧 للمطورين

### **تشغيل محلي:**
```bash
# في هذا المجلد
python -m http.server 8000
# أو
npx serve .
```

### **التحديث:**
1. عدّل الكود الأصلي
2. شغّل: `flutter build web --release`
3. ارفع المجلد الجديد

---

## 📱 متطلبات النظام

### **للآيفون:**
- iOS 11.0 أو أحدث
- Safari (مُوصى به)
- اتصال إنترنت للتحميل الأول

### **للأندرويد:**
- Android 5.0 أو أحدث
- Chrome أو Firefox
- اتصال إنترنت للتحميل الأول

---

## 🎉 مبروك!

الآن لديك تطبيق كامل يعمل على الآيفون بدون الحاجة لـ:
- ❌ Mac أو Xcode
- ❌ حساب Apple Developer
- ❌ App Store
- ❌ برامج معقدة

فقط ارفع واستخدم! 🚀
