import 'package:flutter/material.dart';

class AppLogo extends StatelessWidget {
  final double size;
  final String? logoPath;
  final Color? backgroundColor;
  final Color? iconColor;
  final bool showShadow;

  const AppLogo({
    super.key,
    this.size = 60,
    this.logoPath,
    this.backgroundColor,
    this.iconColor,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        shape: BoxShape.circle,
        boxShadow: showShadow
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: logoPath != null
          ? ClipOval(
              child: Image.asset(
                logoPath!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultLogo();
                },
              ),
            )
          : _buildDefaultLogo(),
    );
  }

  Widget _buildDefaultLogo() {
    return Icon(
      Icons.mosque,
      size: size * 0.6,
      color: iconColor ?? Colors.green[600],
    );
  }
}

// Widget للشعار مع النص
class AppLogoWithText extends StatelessWidget {
  final double logoSize;
  final String? logoPath;
  final String appName;
  final String? subtitle;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;

  const AppLogoWithText({
    super.key,
    this.logoSize = 80,
    this.logoPath,
    this.appName = 'مهام الحاج اليومية',
    this.subtitle,
    this.titleStyle,
    this.subtitleStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AppLogo(
          size: logoSize,
          logoPath: logoPath,
        ),
        const SizedBox(height: 16),
        Text(
          appName,
          style: titleStyle ??
              Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
          textAlign: TextAlign.center,
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 8),
          Text(
            subtitle!,
            style: subtitleStyle ??
                Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

// Widget للشعار الصغير في AppBar
class AppBarLogo extends StatelessWidget {
  final double size;
  final String? logoPath;

  const AppBarLogo({
    super.key,
    this.size = 32,
    this.logoPath,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: logoPath != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                logoPath!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.mosque,
                    size: size * 0.7,
                    color: Colors.green[600],
                  );
                },
              ),
            )
          : Icon(
              Icons.mosque,
              size: size * 0.7,
              color: Colors.green[600],
            ),
    );
  }
}
