# تطبيق مهام الحاج اليومية

تطبيق Flutter يساعد الحجاج على متابعة مهامهم اليومية خلال رحلة الحج المباركة.

## المميزات

### 🕌 الميزات الرئيسية
- **صفحة ترحيبية** مع معلومات المخيم ورقم مفتي الحملة
- **خارطة المخيم** بأرقام الصالات ومواقع الخدمات
- **تذكيرات يومية** لمناسك الحج من اليوم السابع إلى الثاني عشر
- **أسئلة شائعة** مع إجاباتها الشرعية
- **صور توضيحية** للمناسك ومواضع الدعاء
- **واجهة عربية** مصممة خصيصاً للحجاج

### 📅 المهام اليومية المفصلة
1. **مساء السابع** - أعمال يوم التروية (8 ذو الحجة) - الاغتسال والإحرام والتوجه لمنى
2. **فجر التاسع** - التوجه إلى عرفات مع التحضيرات اللازمة
3. **بعد ظهر التاسع** - يوم عرفة والدعاء والذكر مع أفضل الأدعية المأثورة
4. **صباح العاشر** - أعمال يوم النحر بالترتيب الشرعي (رمي، نحر، حلق، طواف)
5. **مساء العاشر** - أعمال أول أيام التشريق (11 ذو الحجة) مع تفاصيل رمي الجمرات
6. **مساء الحادي عشر** - أعمال ثاني أيام التشريق (12 ذو الحجة) وأحكام التعجل
7. **اليوم الثالث عشر** - أعمال المتأخرين وطواف الوداع مع رسائل الختام

## التقنيات المستخدمة

- **Flutter** - إطار العمل الرئيسي
- **Dart** - لغة البرمجة
- **Material Design** - تصميم واجهة المستخدم
- **flutter_localizations** - دعم اللغة العربية
- **shared_preferences** - حفظ الإعدادات
- **url_launcher** - فتح روابط الاتصال
- **intl** - تنسيق التواريخ والأوقات

## البنية التقنية

```
lib/
├── main.dart                 # نقطة البداية
├── models/
│   └── hajj_task.dart       # نموذج البيانات
├── screens/
│   ├── home_screen.dart     # الشاشة الرئيسية
│   ├── welcome_screen.dart  # الشاشة الترحيبية
│   └── task_detail_screen.dart # تفاصيل المهمة
├── widgets/
│   ├── task_card.dart       # بطاقة المهمة
│   └── camp_map.dart        # خارطة المخيم
├── data/
│   └── hajj_tasks_data.dart # بيانات المهام
└── utils/
    └── date_helper.dart     # مساعد التواريخ
```

## التشغيل

### المتطلبات
- Flutter SDK (3.7.2 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- جهاز Android أو iOS أو محاكي

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd hajj
```

2. **تحميل التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

### الاختبار
```bash
# تشغيل الاختبارات
flutter test

# فحص الكود
flutter analyze
```

## إضافة الصور

لإضافة الصور التوضيحية:

1. ضع الصور في مجلد `assets/images/`
2. تأكد من الأسماء التالية:
   - `dua_position.jpg` - موضع الوقوف للدعاء
   - `jamarat_map.jpg` - خريطة الجمرات
   - `kaaba.jpg` - الكعبة المشرفة
   - `mina_tents.jpg` - مخيمات منى
   - `arafat_mountain.jpg` - جبل عرفات

## التخصيص

### تغيير معلومات المخيم
عدّل الملف `lib/data/hajj_tasks_data.dart`:

```dart
static final CampInfo campInfo = CampInfo(
  muftiNumber: '+966 50 123 4567', // رقم المفتي
  campName: 'اسم المخيم',
  hallNumbers: {
    // أرقام الصالات
  },
  serviceLocations: {
    // مواقع الخدمات
  },
);
```

### إضافة مهام جديدة
أضف مهام جديدة في `getTasks()` بنفس الملف.

## المحتوى الشرعي

### 🕌 الأدعية المأثورة
- **التلبية**: "لبيك اللهم لبيك، لبيك لا شريك لك لبيك، إن الحمد والنعمة لك والملك، لا شريك لك"
- **دعاء يوم عرفة**: "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير"
- **التكبير عند الرمي**: "الله أكبر" مع كل حصاة

### 📚 المصادر الشرعية
- أحكام الحج مستقاة من الكتاب والسنة
- فتاوى معتمدة من علماء المملكة العربية السعودية
- إرشادات وزارة الحج والعمرة

### ❓ الأسئلة الشائعة المتضمنة
- أوقات رمي الجمرات
- أحكام المبيت في منى
- شروط التعجل والتأخير
- حكم طواف الوداع
- أحكام التحلل الأصغر والأكبر

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة الاختبارات المناسبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومخصص لخدمة الحجاج.

## الدعم

للدعم والاستفسارات، يرجى فتح issue في المشروع.

---

**تقبل الله حجكم وجعله مبروراً** 🕋
