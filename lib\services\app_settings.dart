import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_constants.dart';

class AppSettings {
  static SharedPreferences? _prefs;

  // تهيئة الإعدادات
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // التحقق من أول تشغيل للتطبيق
  static bool get isFirstLaunch {
    return _prefs?.getBool(AppConstants.prefsKeyFirstLaunch) ?? true;
  }

  static Future<void> setFirstLaunchCompleted() async {
    await _prefs?.setBool(AppConstants.prefsKeyFirstLaunch, false);
  }

  // إعدادات التنبيهات
  static bool get notificationsEnabled {
    return _prefs?.getBool(AppConstants.prefsKeyNotificationsEnabled) ?? true;
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await _prefs?.setBool(AppConstants.prefsKeyNotificationsEnabled, enabled);
  }

  // المهام المكتملة
  static List<String> get completedTasks {
    return _prefs?.getStringList(AppConstants.prefsKeyCompletedTasks) ?? [];
  }

  static Future<void> addCompletedTask(String taskId) async {
    final completed = completedTasks;
    if (!completed.contains(taskId)) {
      completed.add(taskId);
      await _prefs?.setStringList(AppConstants.prefsKeyCompletedTasks, completed);
    }
  }

  static Future<void> removeCompletedTask(String taskId) async {
    final completed = completedTasks;
    completed.remove(taskId);
    await _prefs?.setStringList(AppConstants.prefsKeyCompletedTasks, completed);
  }

  static bool isTaskCompleted(String taskId) {
    return completedTasks.contains(taskId);
  }

  // اللغة المختارة
  static String get selectedLanguage {
    return _prefs?.getString(AppConstants.prefsKeySelectedLanguage) ?? 'ar';
  }

  static Future<void> setSelectedLanguage(String language) async {
    await _prefs?.setString(AppConstants.prefsKeySelectedLanguage, language);
  }

  // حفظ معلومات المخيم المخصصة
  static Future<void> saveCampInfo({
    String? muftiNumber,
    String? campName,
  }) async {
    if (muftiNumber != null) {
      await _prefs?.setString('camp_mufti_number', muftiNumber);
    }
    if (campName != null) {
      await _prefs?.setString('camp_name', campName);
    }
  }

  static String? get savedMuftiNumber {
    return _prefs?.getString('camp_mufti_number');
  }

  static String? get savedCampName {
    return _prefs?.getString('camp_name');
  }

  // إعدادات العرض
  static Future<void> setFontSize(double fontSize) async {
    await _prefs?.setDouble('font_size', fontSize);
  }

  static double get fontSize {
    return _prefs?.getDouble('font_size') ?? AppConstants.bodyFontSize;
  }

  // آخر تاريخ فتح للتطبيق
  static Future<void> updateLastOpenDate() async {
    await _prefs?.setString('last_open_date', DateTime.now().toIso8601String());
  }

  static DateTime? get lastOpenDate {
    final dateString = _prefs?.getString('last_open_date');
    return dateString != null ? DateTime.parse(dateString) : null;
  }

  // عداد مرات فتح التطبيق
  static Future<void> incrementOpenCount() async {
    final count = openCount + 1;
    await _prefs?.setInt('open_count', count);
  }

  static int get openCount {
    return _prefs?.getInt('open_count') ?? 0;
  }

  // مسح جميع البيانات
  static Future<void> clearAllData() async {
    await _prefs?.clear();
  }

  // نسخ احتياطي من الإعدادات
  static Map<String, dynamic> exportSettings() {
    return {
      'notifications_enabled': notificationsEnabled,
      'completed_tasks': completedTasks,
      'selected_language': selectedLanguage,
      'font_size': fontSize,
      'camp_mufti_number': savedMuftiNumber,
      'camp_name': savedCampName,
    };
  }

  // استيراد الإعدادات
  static Future<void> importSettings(Map<String, dynamic> settings) async {
    if (settings['notifications_enabled'] != null) {
      await setNotificationsEnabled(settings['notifications_enabled']);
    }
    if (settings['completed_tasks'] != null) {
      await _prefs?.setStringList(
        AppConstants.prefsKeyCompletedTasks,
        List<String>.from(settings['completed_tasks']),
      );
    }
    if (settings['selected_language'] != null) {
      await setSelectedLanguage(settings['selected_language']);
    }
    if (settings['font_size'] != null) {
      await setFontSize(settings['font_size']);
    }
    if (settings['camp_mufti_number'] != null) {
      await saveCampInfo(muftiNumber: settings['camp_mufti_number']);
    }
    if (settings['camp_name'] != null) {
      await saveCampInfo(campName: settings['camp_name']);
    }
  }
}
