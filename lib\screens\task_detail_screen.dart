import 'package:flutter/material.dart';
import '../models/hajj_task.dart';
import '../utils/date_helper.dart';

class TaskDetailScreen extends StatefulWidget {
  final HajjTask task;
  final Function(HajjTask)? onTaskUpdated;

  const TaskDetailScreen({
    super.key,
    required this.task,
    this.onTaskUpdated,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen> {
  late HajjTask currentTask;

  @override
  void initState() {
    super.initState();
    currentTask = widget.task;
  }

  @override
  Widget build(BuildContext context) {
    final isToday = DateHelper.isToday(currentTask.scheduledDate);
    final dateDescription = DateHelper.getDateDescription(currentTask.scheduledDate);
    final timeOfDayText = DateHelper.getTimeOfDayText(currentTask.timeOfDay);

    return Scaffold(
      appBar: AppBar(
        title: Text(currentTask.title),
        backgroundColor: _getAppBarColor(currentTask.type),
        foregroundColor: Colors.white,
        actions: [
          if (!currentTask.isCompleted && isToday)
            IconButton(
              onPressed: _markAsCompleted,
              icon: const Icon(Icons.check_circle_outline),
              tooltip: 'تم الإنجاز',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات التاريخ والوقت
            _buildDateTimeCard(dateDescription, timeOfDayText, isToday),
            
            const SizedBox(height: 16),
            
            // الوصف
            _buildDescriptionCard(),
            
            const SizedBox(height: 16),
            
            // المحتوى الرئيسي
            _buildContentCard(),
            
            // الصور (إن وجدت)
            if (currentTask.images != null && currentTask.images!.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildImagesCard(),
            ],
            
            // الأسئلة الشائعة (إن وجدت)
            if (currentTask.faqs != null && currentTask.faqs!.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildFaqsCard(),
            ],
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeCard(String dateDescription, String timeOfDayText, bool isToday) {
    return Card(
      color: isToday ? Colors.blue[50] : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.schedule,
              color: isToday ? Colors.blue[600] : Colors.grey[600],
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    dateDescription,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isToday ? Colors.blue[700] : null,
                    ),
                  ),
                  Text(
                    timeOfDayText,
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (currentTask.isCompleted)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'تم الإنجاز',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getTaskTypeIcon(currentTask.type),
                  color: _getTaskTypeColor(currentTask.type),
                ),
                const SizedBox(width: 8),
                Text(
                  'الوصف',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              currentTask.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.list_alt,
                  color: Colors.blue,
                ),
                const SizedBox(width: 8),
                Text(
                  'التفاصيل',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...currentTask.content.map((item) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 8, left: 8),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: _getTaskTypeColor(currentTask.type),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 16,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.image,
                  color: Colors.purple,
                ),
                const SizedBox(width: 8),
                Text(
                  'الصور التوضيحية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // TODO: إضافة عرض الصور الفعلية
            ...currentTask.images!.map((imagePath) => Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple[200]!),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.image,
                    color: Colors.purple,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      imagePath.split('/').last,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildFaqsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.help_outline,
                  color: Colors.teal,
                ),
                const SizedBox(width: 8),
                Text(
                  'أسئلة شائعة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...currentTask.faqs!.map((faq) {
              final parts = faq.split('\n');
              final question = parts[0];
              final answer = parts.length > 1 ? parts[1] : '';
              
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.teal[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.teal[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      question,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    if (answer.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        answer,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _markAsCompleted() {
    setState(() {
      currentTask = currentTask.copyWith(isCompleted: true);
    });
    
    if (widget.onTaskUpdated != null) {
      widget.onTaskUpdated!(currentTask);
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تسجيل إنجاز المهمة'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Color _getAppBarColor(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return Colors.blue[600]!;
      case TaskType.ritual:
        return Colors.green[600]!;
      case TaskType.prayer:
        return Colors.purple[600]!;
      case TaskType.celebration:
        return Colors.orange[600]!;
      case TaskType.faq:
        return Colors.teal[600]!;
      case TaskType.farewell:
        return Colors.red[600]!;
    }
  }

  IconData _getTaskTypeIcon(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return Icons.waving_hand;
      case TaskType.ritual:
        return Icons.mosque;
      case TaskType.prayer:
        return Icons.favorite;
      case TaskType.celebration:
        return Icons.celebration;
      case TaskType.faq:
        return Icons.help;
      case TaskType.farewell:
        return Icons.flight_takeoff;
    }
  }

  Color _getTaskTypeColor(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return Colors.blue;
      case TaskType.ritual:
        return Colors.green;
      case TaskType.prayer:
        return Colors.purple;
      case TaskType.celebration:
        return Colors.orange;
      case TaskType.faq:
        return Colors.teal;
      case TaskType.farewell:
        return Colors.red;
    }
  }
}
