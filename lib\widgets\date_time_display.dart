import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';

class DateTimeDisplay extends StatefulWidget {
  final bool showLogo;
  final String? logoPath;

  const DateTimeDisplay({super.key, this.showLogo = true, this.logoPath});

  @override
  State<DateTimeDisplay> createState() => _DateTimeDisplayState();
}

class _DateTimeDisplayState extends State<DateTimeDisplay> {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [Colors.green[400]!, Colors.green[600]!],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // الشعار
          if (widget.showLogo) ...[_buildLogo(), const SizedBox(width: 16)],

          // معلومات التاريخ والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTimeDisplay(),
                const SizedBox(height: 4),
                _buildDateDisplay(),
                const SizedBox(height: 4),
                _buildHijriDate(),
              ],
            ),
          ),

          // أيقونة الوقت
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(_getTimeIcon(), color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child:
          widget.logoPath != null
              ? ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  widget.logoPath!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultLogo();
                  },
                ),
              )
              : _buildDefaultLogo(),
    );
  }

  Widget _buildDefaultLogo() {
    return Icon(Icons.mosque, color: Colors.green[600], size: 30);
  }

  Widget _buildTimeDisplay() {
    final timeFormat = DateFormat('HH:mm:ss', 'ar');
    return Text(
      timeFormat.format(_currentTime),
      style: const TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        fontFamily: 'monospace',
      ),
    );
  }

  Widget _buildDateDisplay() {
    final dateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');
    return Text(
      dateFormat.format(_currentTime),
      style: const TextStyle(
        fontSize: 14,
        color: Colors.white70,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _buildHijriDate() {
    // تاريخ هجري تقريبي - يمكن تحسينه باستخدام مكتبة متخصصة
    final hijriYear = _currentTime.year - 579; // تقريب بسيط
    return Text(
      'التاريخ الهجري: $hijriYear هـ (تقريبي)',
      style: const TextStyle(fontSize: 12, color: Colors.white60),
    );
  }

  IconData _getTimeIcon() {
    final hour = _currentTime.hour;

    if (hour >= 5 && hour < 12) {
      return Icons.wb_sunny; // صباح
    } else if (hour >= 12 && hour < 15) {
      return Icons.wb_sunny_outlined; // ظهر
    } else if (hour >= 15 && hour < 18) {
      return Icons.wb_twilight; // عصر
    } else if (hour >= 18 && hour < 21) {
      return Icons.nights_stay; // مغرب
    } else {
      return Icons.bedtime; // ليل
    }
  }
}

// Widget مبسط لعرض الوقت فقط
class SimpleTimeDisplay extends StatefulWidget {
  const SimpleTimeDisplay({super.key});

  @override
  State<SimpleTimeDisplay> createState() => _SimpleTimeDisplayState();
}

class _SimpleTimeDisplayState extends State<SimpleTimeDisplay> {
  late Timer _timer;
  DateTime _currentTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat('HH:mm', 'ar');
    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.access_time, size: 16, color: Colors.green[700]),
          const SizedBox(width: 4),
          Text(
            '${timeFormat.format(_currentTime)} - ${dateFormat.format(_currentTime)}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
