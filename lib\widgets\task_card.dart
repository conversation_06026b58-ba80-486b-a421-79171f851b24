import 'package:flutter/material.dart';
import '../models/hajj_task.dart';
import '../utils/date_helper.dart';

class TaskCard extends StatelessWidget {
  final HajjTask task;
  final VoidCallback? onTap;

  const TaskCard({super.key, required this.task, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isToday = DateHelper.isToday(task.scheduledDate);
    final isPast = DateHelper.isPast(task.scheduledDate);
    final dateDescription = DateHelper.getDateDescription(task.scheduledDate);
    final timeOfDayText = DateHelper.getTimeOfDayText(task.timeOfDay);

    return Card(
      elevation: isToday ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: _getCardColor(isToday, isPast, task.isCompleted),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والحالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isToday ? Colors.white : null,
                      ),
                    ),
                  ),
                  if (task.isCompleted)
                    Icon(
                      Icons.check_circle,
                      color: isToday ? Colors.white : Colors.green,
                    ),
                  if (isToday && !task.isCompleted)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'اليوم',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // الوصف
              Text(
                task.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isToday ? Colors.white70 : Colors.grey[600],
                ),
              ),

              const SizedBox(height: 12),

              // التاريخ والوقت
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: isToday ? Colors.white70 : Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    dateDescription,
                    style: TextStyle(
                      fontSize: 12,
                      color: isToday ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    _getTimeIcon(task.timeOfDay),
                    size: 16,
                    color: isToday ? Colors.white70 : Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    timeOfDayText,
                    style: TextStyle(
                      fontSize: 12,
                      color: isToday ? Colors.white70 : Colors.grey[600],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // نوع المهمة
              Row(
                children: [
                  Icon(
                    _getTaskTypeIcon(task.type),
                    size: 16,
                    color:
                        isToday ? Colors.white70 : _getTaskTypeColor(task.type),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getTaskTypeText(task.type),
                    style: TextStyle(
                      fontSize: 12,
                      color:
                          isToday
                              ? Colors.white70
                              : _getTaskTypeColor(task.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  if (task.faqs != null && task.faqs!.isNotEmpty)
                    Icon(
                      Icons.help_outline,
                      size: 16,
                      color: isToday ? Colors.white70 : Colors.blue,
                    ),
                  if (task.images != null && task.images!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Icon(
                        Icons.image,
                        size: 16,
                        color: isToday ? Colors.white70 : Colors.purple,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color? _getCardColor(bool isToday, bool isPast, bool isCompleted) {
    if (isCompleted) {
      return Colors.green[100];
    } else if (isToday) {
      return Colors.blue[600];
    } else if (isPast) {
      return Colors.grey[200];
    }
    return null;
  }

  IconData _getTimeIcon(String timeOfDay) {
    switch (timeOfDay.toLowerCase()) {
      case 'morning':
        return Icons.wb_sunny;
      case 'afternoon':
        return Icons.wb_sunny_outlined;
      case 'evening':
        return Icons.nights_stay;
      case 'night':
        return Icons.bedtime;
      default:
        return Icons.access_time;
    }
  }

  IconData _getTaskTypeIcon(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return Icons.waving_hand;
      case TaskType.ritual:
        return Icons.mosque;
      case TaskType.prayer:
        return Icons.favorite;
      case TaskType.celebration:
        return Icons.celebration;
      case TaskType.faq:
        return Icons.help;
      case TaskType.farewell:
        return Icons.flight_takeoff;
    }
  }

  Color _getTaskTypeColor(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return Colors.blue;
      case TaskType.ritual:
        return Colors.green;
      case TaskType.prayer:
        return Colors.purple;
      case TaskType.celebration:
        return Colors.orange;
      case TaskType.faq:
        return Colors.teal;
      case TaskType.farewell:
        return Colors.red;
    }
  }

  String _getTaskTypeText(TaskType type) {
    switch (type) {
      case TaskType.welcome:
        return 'ترحيب';
      case TaskType.ritual:
        return 'مناسك';
      case TaskType.prayer:
        return 'دعاء';
      case TaskType.celebration:
        return 'احتفال';
      case TaskType.faq:
        return 'أسئلة شائعة';
      case TaskType.farewell:
        return 'وداع';
    }
  }
}
