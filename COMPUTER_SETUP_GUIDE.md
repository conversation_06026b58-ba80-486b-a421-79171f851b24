# 💻 دليل تشغيل التطبيق على الكمبيوتر

## 📦 الخطوة 1: تحميل واستخراج الملفات

### **تحديد موقع الملف:**
الملف المضغوط موجود في مجلد المشروع:
```
تطبيق_المهام_اليومية_للحج_v1.0.zip
```

### **استخراج الملفات:**

#### **في Windows:**
1. **انقر بزر الماوس الأيمن** على الملف المضغوط
2. **اختر "استخراج الكل"** أو **"Extract All"**
3. **اختر مجلد الوجهة** (مثل سطح المكتب)
4. **اضغط "استخراج"**

#### **في Mac:**
1. **انقر مرتين** على الملف المضغوط
2. **سيتم استخراج الملفات** تلقائياً

#### **في Linux:**
```bash
unzip تطبيق_المهام_اليومية_للحج_v1.0.zip
```

### **النتيجة:**
ستحصل على مجلد `hajj_app_release` يحتوي على:
```
hajj_app_release/
├── web_version/           # التطبيق الجاهز
├── documentation/         # الأدلة
├── اقرأني_أولاً.md
└── ملفات أخرى...
```

---

## 🚀 الخطوة 2: تشغيل التطبيق

### **🎯 الطريقة الأولى: خادم Python (الأسهل)**

#### **المتطلبات:**
- Python مثبت على الكمبيوتر

#### **الخطوات:**
1. **افتح Command Prompt** أو **Terminal**
2. **اذهب لمجلد التطبيق:**
   ```bash
   cd hajj_app_release/web_version
   ```
3. **شغّل الخادم:**
   ```bash
   python -m http.server 8000
   ```
4. **افتح المتصفح** واذهب إلى:
   ```
   http://localhost:8000
   ```

#### **أو استخدم السكريبت الجاهز:**
1. **انسخ ملف** `run_on_computer.bat` إلى مجلد `hajj_app_release`
2. **انقر مرتين** على الملف
3. **اختر الخيار 1** (Python Server)

### **🌐 الطريقة الثانية: فتح مباشر في المتصفح**

#### **الخطوات:**
1. **اذهب لمجلد** `hajj_app_release/web_version`
2. **انقر مرتين** على ملف `index.html`
3. **سيفتح في المتصفح** الافتراضي

#### **⚠️ ملاحظة:**
قد لا تعمل جميع الميزات بهذه الطريقة بسبب قيود الأمان في المتصفحات.

### **⚡ الطريقة الثالثة: خادم Node.js**

#### **المتطلبات:**
- Node.js مثبت على الكمبيوتر

#### **الخطوات:**
1. **ثبّت serve:**
   ```bash
   npm install -g serve
   ```
2. **اذهب لمجلد التطبيق:**
   ```bash
   cd hajj_app_release/web_version
   ```
3. **شغّل الخادم:**
   ```bash
   serve .
   ```
4. **افتح الرابط** الذي يظهر

---

## 🎨 الخطوة 3: تحسين التجربة

### **📱 محاكاة الآيفون في المتصفح:**

#### **في Chrome:**
1. **اضغط F12** لفتح Developer Tools
2. **اضغط على أيقونة الهاتف** 📱 (أعلى اليسار)
3. **اختر "iPhone"** من القائمة المنسدلة
4. **أعد تحميل الصفحة**

#### **في Firefox:**
1. **اضغط F12** لفتح Developer Tools
2. **اضغط على أيقونة الهاتف** 📱
3. **اختر "iPhone"** من القائمة
4. **أعد تحميل الصفحة**

### **🔧 إعدادات مُوصى بها:**
- **الدقة:** 375 x 812 (iPhone X)
- **التكبير:** 100%
- **الاتجاه:** عمودي

---

## 🎯 ما ستحصل عليه

### **✅ جميع ميزات التطبيق:**
- **المهام اليومية للحج** (8 أيام كاملة)
- **خارطة المخيم** القابلة للتعديل
- **رقم المفتي** القابل للتغيير
- **واجهة عربية** كاملة
- **حفظ الإعدادات** في المتصفح

### **🎨 تجربة مستخدم ممتازة:**
- **تصميم متجاوب** مع شاشة الكمبيوتر
- **سرعة عالية** في التحميل
- **عمل بدون إنترنت** بعد التحميل الأول
- **حفظ تلقائي** لجميع التغييرات

---

## 🔧 حل المشاكل الشائعة

### **❌ Python غير موجود:**
#### **تحميل Python:**
1. **اذهب إلى** [python.org](https://python.org)
2. **حمّل أحدث إصدار**
3. **ثبّته مع تفعيل "Add to PATH"**

### **❌ الصفحة لا تحمّل:**
#### **الحلول:**
1. **تأكد من تشغيل الخادم** بشكل صحيح
2. **تحقق من الرابط:** `http://localhost:8000`
3. **جرب متصفح آخر**
4. **أعد تشغيل الخادم**

### **❌ الميزات لا تعمل:**
#### **الحلول:**
1. **استخدم خادم محلي** وليس فتح مباشر
2. **تأكد من تفعيل JavaScript**
3. **امسح cache المتصفح**
4. **جرب وضع التصفح الخاص**

---

## 📋 سكريبتات مساعدة

### **🔧 ملفات جاهزة للاستخدام:**

#### **run_on_computer.bat (Windows):**
```batch
# سكريبت تشغيل تلقائي
# يوفر خيارات متعددة للتشغيل
# سهل الاستخدام
```

#### **للاستخدام:**
1. **انسخ الملف** إلى مجلد `hajj_app_release`
2. **انقر مرتين** عليه
3. **اختر طريقة التشغيل**

---

## 🎉 نصائح للاستخدام الأمثل

### **🖥️ على الكمبيوتر:**
- **استخدم Chrome أو Firefox** للحصول على أفضل تجربة
- **فعّل وضع الهاتف** لمحاكاة الآيفون
- **استخدم خادم محلي** وليس فتح مباشر
- **احفظ الرابط** في المفضلة للوصول السريع

### **💾 حفظ البيانات:**
- **الإعدادات تُحفظ** في المتصفح تلقائياً
- **لا تحتاج قاعدة بيانات** خارجية
- **البيانات آمنة** ومحفوظة محلياً

### **🔄 التحديثات:**
- **استبدل مجلد web_version** بالإصدار الجديد
- **الإعدادات ستبقى** محفوظة
- **لا تحتاج إعادة تثبيت**

---

## 📱 مقارنة مع الآيفون

| الميزة | الكمبيوتر | الآيفون |
|--------|-----------|---------|
| **سهولة التثبيت** | متوسطة | سهلة جداً |
| **الأداء** | ممتاز | ممتاز |
| **الميزات** | كاملة | كاملة |
| **التنقل** | ماوس/لوحة مفاتيح | لمس |
| **الحجم** | شاشة كبيرة | محمول |

---

## 🎯 الخلاصة

### **✅ المميزات:**
- **تشغيل سهل** على أي كمبيوتر
- **جميع الميزات متاحة**
- **لا يحتاج تثبيت** برامج معقدة
- **يعمل بدون إنترنت**

### **📋 الخطوات المختصرة:**
1. **استخرج الملف المضغوط**
2. **شغّل خادم محلي** في مجلد web_version
3. **افتح** `http://localhost:8000`
4. **استمتع بالتطبيق!**

---

**💻 مبروك! الآن يمكنك استخدام تطبيق المهام اليومية للحج على الكمبيوتر**

*للحصول على أفضل تجربة، استخدم وضع محاكاة الهاتف في المتصفح* 📱✨
