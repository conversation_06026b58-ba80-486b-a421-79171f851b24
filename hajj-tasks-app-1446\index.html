<!DOCTYPE html>
<html>
<head>
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="تطبيق المهام اليومية للحج - حملة الفرقان 1446">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="theme-color" content="#4CAF50">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="مهام الحج">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>المهام اليومية للحج - حملة الفرقان</title>
  <link rel="manifest" href="manifest.json">
  
  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      direction: rtl;
    }
    
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
      z-index: 9999;
    }
    
    .kaaba-icon {
      width: 80px;
      height: 80px;
      background: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.2);
      animation: pulse 2s infinite;
    }
    
    .kaaba-icon::before {
      content: "🕋";
      font-size: 40px;
    }
    
    .loading-text {
      color: white;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
      text-align: center;
    }
    
    .loading-subtitle {
      color: rgba(255,255,255,0.8);
      font-size: 16px;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255,255,255,0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .fade-out {
      opacity: 0;
      transition: opacity 0.5s ease-out;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading-container">
    <div class="kaaba-icon"></div>
    <div class="loading-text">المهام اليومية للحج</div>
    <div class="loading-subtitle">حملة الفرقان 1446 هـ</div>
    <div class="spinner"></div>
  </div>
  
  <script>
    window.addEventListener('flutter-first-frame', function () {
      const loading = document.getElementById('loading');
      loading.classList.add('fade-out');
      setTimeout(() => {
        loading.style.display = 'none';
      }, 500);
    });
    
    // Fallback: hide loading after 10 seconds
    setTimeout(() => {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.classList.add('fade-out');
        setTimeout(() => {
          loading.style.display = 'none';
        }, 500);
      }
    }, 10000);
  </script>
  
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
