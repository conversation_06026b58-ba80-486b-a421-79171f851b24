import 'package:flutter/material.dart';
import '../services/app_settings.dart';

class CampMapEditorScreen extends StatefulWidget {
  const CampMapEditorScreen({super.key});

  @override
  State<CampMapEditorScreen> createState() => _CampMapEditorScreenState();
}

class _CampMapEditorScreenState extends State<CampMapEditorScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  Map<String, String> _hallNumbers = {};
  Map<String, String> _serviceLocations = {};
  
  final Map<String, TextEditingController> _hallControllers = {};
  final Map<String, TextEditingController> _serviceControllers = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  void _loadData() {
    _hallNumbers = Map.from(AppSettings.campHallNumbers);
    _serviceLocations = Map.from(AppSettings.campServiceLocations);
    
    // إنشاء controllers للصالات
    for (var entry in _hallNumbers.entries) {
      _hallControllers[entry.key] = TextEditingController(text: entry.value);
    }
    
    // إنشاء controllers للخدمات
    for (var entry in _serviceLocations.entries) {
      _serviceControllers[entry.key] = TextEditingController(text: entry.value);
    }
    
    setState(() {});
  }

  @override
  void dispose() {
    _tabController.dispose();
    for (var controller in _hallControllers.values) {
      controller.dispose();
    }
    for (var controller in _serviceControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل خارطة المخيم'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _saveData,
            icon: const Icon(Icons.save),
            tooltip: 'حفظ التغييرات',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset',
                child: Row(
                  children: [
                    Icon(Icons.refresh, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('إعادة تعيين'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_hall',
                child: Row(
                  children: [
                    Icon(Icons.add_home, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('إضافة صالة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'add_service',
                child: Row(
                  children: [
                    Icon(Icons.add_location, color: Colors.green),
                    SizedBox(width: 8),
                    Text('إضافة خدمة'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'أرقام الصالات'),
            Tab(icon: Icon(Icons.location_on), text: 'مواقع الخدمات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildHallNumbersTab(),
          _buildServiceLocationsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _saveData,
        backgroundColor: Colors.green[600],
        child: const Icon(Icons.save, color: Colors.white),
      ),
    );
  }

  Widget _buildHallNumbersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            'أرقام الصالات',
            Icons.home,
            'قم بتعديل أرقام الصالات حسب موقعها في المخيم',
          ),
          const SizedBox(height: 16),
          ..._hallNumbers.keys.map((hallName) => _buildEditableItem(
                title: hallName,
                controller: _hallControllers[hallName]!,
                icon: Icons.meeting_room,
                hintText: 'رقم الصالة (مثل: A1, B2)',
                onDelete: () => _deleteHall(hallName),
              )),
          const SizedBox(height: 16),
          _buildAddButton(
            'إضافة صالة جديدة',
            Icons.add_home,
            () => _addNewHall(),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceLocationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            'مواقع الخدمات',
            Icons.location_on,
            'قم بتعديل مواقع الخدمات والمرافق في المخيم',
          ),
          const SizedBox(height: 16),
          ..._serviceLocations.keys.map((serviceName) => _buildEditableItem(
                title: serviceName,
                controller: _serviceControllers[serviceName]!,
                icon: Icons.place,
                hintText: 'موقع الخدمة (مثل: الطابق الأول)',
                onDelete: () => _deleteService(serviceName),
              )),
          const SizedBox(height: 16),
          _buildAddButton(
            'إضافة خدمة جديدة',
            Icons.add_location,
            () => _addNewService(),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, String description) {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.green[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.green[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditableItem({
    required String title,
    required TextEditingController controller,
    required IconData icon,
    required String hintText,
    required VoidCallback onDelete,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete, color: Colors.red),
                  iconSize: 20,
                  tooltip: 'حذف',
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                isDense: true,
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton(String text, IconData icon, VoidCallback onPressed) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(text),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.green[600],
          side: BorderSide(color: Colors.green[300]!),
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'reset':
        _resetToDefaults();
        break;
      case 'add_hall':
        _addNewHall();
        break;
      case 'add_service':
        _addNewService();
        break;
    }
  }

  void _addNewHall() {
    _showAddDialog(
      title: 'إضافة صالة جديدة',
      nameHint: 'اسم الصالة',
      valueHint: 'رقم الصالة',
      onAdd: (name, value) {
        setState(() {
          _hallNumbers[name] = value;
          _hallControllers[name] = TextEditingController(text: value);
        });
      },
    );
  }

  void _addNewService() {
    _showAddDialog(
      title: 'إضافة خدمة جديدة',
      nameHint: 'اسم الخدمة',
      valueHint: 'موقع الخدمة',
      onAdd: (name, value) {
        setState(() {
          _serviceLocations[name] = value;
          _serviceControllers[name] = TextEditingController(text: value);
        });
      },
    );
  }

  void _showAddDialog({
    required String title,
    required String nameHint,
    required String valueHint,
    required Function(String name, String value) onAdd,
  }) {
    final nameController = TextEditingController();
    final valueController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: nameHint,
                border: const OutlineInputBorder(),
              ),
              textAlign: TextAlign.right,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: valueController,
              decoration: InputDecoration(
                labelText: valueHint,
                border: const OutlineInputBorder(),
              ),
              textAlign: TextAlign.right,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isNotEmpty && valueController.text.isNotEmpty) {
                onAdd(nameController.text.trim(), valueController.text.trim());
                Navigator.pop(context);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _deleteHall(String hallName) {
    _showDeleteConfirmation(
      title: 'حذف الصالة',
      message: 'هل أنت متأكد من حذف "$hallName"؟',
      onConfirm: () {
        setState(() {
          _hallNumbers.remove(hallName);
          _hallControllers[hallName]?.dispose();
          _hallControllers.remove(hallName);
        });
      },
    );
  }

  void _deleteService(String serviceName) {
    _showDeleteConfirmation(
      title: 'حذف الخدمة',
      message: 'هل أنت متأكد من حذف "$serviceName"؟',
      onConfirm: () {
        setState(() {
          _serviceLocations.remove(serviceName);
          _serviceControllers[serviceName]?.dispose();
          _serviceControllers.remove(serviceName);
        });
      },
    );
  }

  void _showDeleteConfirmation({
    required String title,
    required String message,
    required VoidCallback onConfirm,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين'),
        content: const Text('هل أنت متأكد من إعادة تعيين جميع البيانات للقيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await AppSettings.resetCampMapData();
              _loadData();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم إعادة تعيين البيانات بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveData() async {
    try {
      // تحديث البيانات من controllers
      for (var entry in _hallControllers.entries) {
        _hallNumbers[entry.key] = entry.value.text.trim();
      }
      for (var entry in _serviceControllers.entries) {
        _serviceLocations[entry.key] = entry.value.text.trim();
      }

      // حفظ البيانات
      await AppSettings.saveCampMapData(
        hallNumbers: _hallNumbers,
        serviceLocations: _serviceLocations,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ البيانات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
