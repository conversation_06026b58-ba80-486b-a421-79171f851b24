# دليل إضافة الشعار - تطبيق مهام الحاج اليومية

## 🎨 كيفية إضافة شعار التطبيق

### الخطوة 1: تحضير الشعار

#### المواصفات المطلوبة:
- **الصيغة**: PNG مع خلفية شفافة
- **الحجم**: 512x512 بكسل (للجودة العالية)
- **الجودة**: 300 DPI أو أكثر
- **الألوان**: متوافقة مع ألوان التطبيق (أخضر إسلامي #4CAF50)

#### التصميم المقترح:
- **العناصر الإسلامية**: الكعبة المشرفة، الهلال، النجمة
- **الخط العربي**: خط جميل ومقروء
- **البساطة**: تصميم بسيط وواضح
- **الألوان**: أخضر، ذهبي، أبيض

### الخطوة 2: إضافة الشعار للمشروع

1. **احفظ الشعار** في المجلد:
   ```
   assets/logos/app_logo.png
   ```

2. **تأكد من إضافة المجلد** في `pubspec.yaml`:
   ```yaml
   flutter:
     assets:
       - assets/logos/
   ```

3. **قم بتشغيل الأمر**:
   ```bash
   flutter pub get
   ```

### الخطوة 3: أحجام إضافية (اختيارية)

يمكنك إضافة أحجام مختلفة للشعار:

```
assets/logos/
├── app_logo.png          # الحجم الأساسي (512x512)
├── app_logo_small.png    # حجم صغير (128x128)
├── app_logo_medium.png   # حجم متوسط (256x256)
├── app_logo_white.png    # نسخة بيضاء للخلفيات الداكنة
└── app_logo_icon.png     # أيقونة مربعة (64x64)
```

## 🖼️ أماكن ظهور الشعار

### 1. الشاشة الرئيسية
- يظهر في أعلى الشاشة مع التاريخ والوقت
- الحجم: 50x50 بكسل
- مع خلفية بيضاء دائرية

### 2. الشاشة الترحيبية
- يظهر كشعار كبير مع اسم المخيم
- الحجم: 80x80 بكسل
- مع تأثيرات الظل

### 3. شاشة حول التطبيق
- يظهر في مربع حوار "حول التطبيق"
- الحجم: 48x48 بكسل

## 🔧 التخصيص المتقدم

### تغيير مسار الشعار في الكود:

#### في الشاشة الرئيسية:
```dart
DateTimeDisplay(
  logoPath: 'assets/logos/your_logo.png', // غير هذا المسار
),
```

#### في الشاشة الترحيبية:
```dart
AppLogoWithText(
  logoPath: 'assets/logos/your_logo.png', // غير هذا المسار
  appName: campInfo.campName,
  subtitle: 'تقبل الله حجكم وجعله مبروراً',
),
```

### إضافة شعار مخصص لكل مخيم:

يمكنك إضافة شعارات مختلفة حسب المخيم:

```dart
// في ملف hajj_tasks_data.dart
static final CampInfo campInfo = CampInfo(
  // ... معلومات أخرى
  logoPath: 'assets/logos/camp_specific_logo.png', // شعار المخيم
);
```

## 🎨 أدوات التصميم المقترحة

### برامج مجانية:
- **GIMP**: برنامج تحرير صور مجاني
- **Canva**: أداة تصميم أونلاين
- **Figma**: أداة تصميم واجهات

### برامج احترافية:
- **Adobe Illustrator**: للتصميم المتجهي
- **Adobe Photoshop**: لتحرير الصور
- **Sketch**: للتصميم على Mac

## 📱 اختبار الشعار

### تأكد من:
1. **الوضوح**: الشعار واضح في جميع الأحجام
2. **التباين**: يظهر بوضوح على الخلفيات المختلفة
3. **التوافق**: متوافق مع ألوان التطبيق
4. **الجودة**: لا يفقد جودته عند التصغير

### أحجام الاختبار:
- 16x16 (أيقونة صغيرة)
- 32x32 (أيقونة متوسطة)
- 64x64 (أيقونة كبيرة)
- 128x128 (شعار صغير)
- 256x256 (شعار متوسط)
- 512x512 (شعار كبير)

## 🔄 التحديث والصيانة

### عند تغيير الشعار:
1. استبدل الملف في `assets/logos/`
2. احتفظ بنفس اسم الملف
3. قم بتشغيل `flutter pub get`
4. أعد تشغيل التطبيق

### النسخ الاحتياطية:
- احتفظ بنسخة أصلية عالية الجودة
- احفظ ملفات المصدر (AI, PSD, etc.)
- وثق إصدارات الشعار المختلفة

## 🎯 نصائح للتصميم

### للشعار الإسلامي:
- استخدم الخط العربي الجميل
- أضف عناصر إسلامية بسيطة
- تجنب الصور المعقدة
- استخدم ألوان هادئة ومريحة

### للقراءة:
- تأكد من وضوح النص في الأحجام الصغيرة
- استخدم تباين جيد بين النص والخلفية
- تجنب الخطوط المزخرفة المعقدة

### للهوية البصرية:
- حافظ على الاتساق مع ألوان التطبيق
- استخدم نفس الطابع في جميع العناصر
- اجعل الشعار يعكس روح الحج والعبادة

---

## 📞 الدعم

إذا واجهت أي مشاكل في إضافة الشعار:

1. تأكد من صحة مسار الملف
2. تحقق من صيغة الملف (PNG مفضل)
3. تأكد من إضافة المجلد في pubspec.yaml
4. أعد تشغيل `flutter pub get`

**تقبل الله حجكم وجعله مبروراً** 🕋
