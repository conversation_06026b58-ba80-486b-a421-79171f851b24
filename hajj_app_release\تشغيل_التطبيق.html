<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل تطبيق المهام اليومية للحج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        
        h1 {
            color: #2E7D32;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }
        
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-size: 18px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .button.secondary {
            background: #2196F3;
        }
        
        .button.secondary:hover {
            background: #1976D2;
        }
        
        .instructions {
            background: #f5f5f5;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: right;
        }
        
        .step {
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #4CAF50;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🕋</div>
        <h1>تطبيق المهام اليومية للحج</h1>
        <p class="subtitle">حملة الفرقان</p>
        
        <div class="success">
            <strong>🎉 مرحباً بك!</strong><br>
            اختر طريقة تشغيل التطبيق على الكمبيوتر
        </div>
        
        <div style="margin: 30px 0;">
            <a href="web_version/index.html" class="button" onclick="openApp()">
                🚀 تشغيل التطبيق مباشرة
            </a>
            <br>
            <button class="button secondary" onclick="showInstructions()">
                📋 تعليمات الخادم المحلي
            </button>
        </div>
        
        <div class="warning">
            <strong>⚠️ ملاحظة مهمة:</strong><br>
            للحصول على أفضل تجربة، يُنصح بتشغيل خادم محلي بدلاً من الفتح المباشر
        </div>
        
        <div id="instructions" class="instructions" style="display: none;">
            <h3>📋 تعليمات تشغيل الخادم المحلي</h3>
            
            <div class="step">
                <strong>الخطوة 1:</strong> افتح Command Prompt أو PowerShell في مجلد web_version
            </div>
            
            <div class="step">
                <strong>الخطوة 2:</strong> اكتب الأمر التالي:<br>
                <code style="background: #f0f0f0; padding: 5px; border-radius: 4px;">python -m http.server 8000</code>
            </div>
            
            <div class="step">
                <strong>الخطوة 3:</strong> افتح المتصفح واذهب إلى:<br>
                <code style="background: #f0f0f0; padding: 5px; border-radius: 4px;">http://localhost:8000</code>
            </div>
            
            <div class="step">
                <strong>للإيقاف:</strong> اضغط Ctrl+C في نافذة الأوامر
            </div>
            
            <div style="margin-top: 20px;">
                <strong>📱 لمحاكاة الآيفون:</strong><br>
                اضغط F12 في المتصفح ← اختر أيقونة الهاتف 📱 ← اختر iPhone
            </div>
        </div>
        
        <div style="margin-top: 30px; color: #666; font-size: 14px;">
            <p>🎯 <strong>ميزات التطبيق:</strong></p>
            <p>📅 8 مهام يومية للحج • 🗺️ خارطة مخيم قابلة للتعديل • 📞 رقم مفتي قابل للتغيير</p>
            <p style="margin-top: 20px; color: #4CAF50; font-weight: bold;">
                تقبل الله حجكم وجعله مبروراً 🤲
            </p>
        </div>
    </div>

    <script>
        function openApp() {
            // محاولة فتح التطبيق
            setTimeout(() => {
                alert('🎉 تم فتح التطبيق!\n\n💡 نصيحة: للحصول على أفضل تجربة:\n• اضغط F12\n• اختر أيقونة الهاتف 📱\n• اختر iPhone من القائمة');
            }, 1000);
        }
        
        function showInstructions() {
            const instructions = document.getElementById('instructions');
            if (instructions.style.display === 'none') {
                instructions.style.display = 'block';
            } else {
                instructions.style.display = 'none';
            }
        }
        
        // رسالة ترحيب
        window.onload = function() {
            console.log('🕋 تطبيق المهام اليومية للحج - حملة الفرقان');
            console.log('📱 للحصول على أفضل تجربة، استخدم وضع محاكاة الهاتف');
        };
    </script>
</body>
</html>
