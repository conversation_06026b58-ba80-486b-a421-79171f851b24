import 'package:flutter/material.dart';
import '../models/hajj_task.dart';
import '../data/hajj_tasks_data.dart';
import '../widgets/task_card.dart';
import '../widgets/date_time_display.dart';
import '../screens/welcome_screen.dart';
import '../screens/task_detail_screen.dart';
import '../utils/date_helper.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late List<HajjTask> tasks;
  late TabController _tabController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    tasks = HajjTasksData.getTasks();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            // شعار الحملة (سيتم إضافة الصورة لاحقاً)
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(Icons.mosque, color: Colors.green, size: 20),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'المهام اليومية للحج - حملة الفرقان',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.today), text: 'اليوم'),
            Tab(icon: Icon(Icons.list), text: 'جميع المهام'),
            Tab(icon: Icon(Icons.check_circle), text: 'المنجزة'),
          ],
        ),
      ),
      body: Column(
        children: [
          // عرض التاريخ والوقت مع الشعار
          const DateTimeDisplay(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTodayTasks(),
                _buildAllTasks(),
                _buildCompletedTasks(),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onBottomNavTap,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.green[600],
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'الرئيسية'),
          BottomNavigationBarItem(
            icon: Icon(Icons.waving_hand),
            label: 'الترحيب',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.info), label: 'حول التطبيق'),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showTodayReminder,
        backgroundColor: Colors.green[600],
        child: const Icon(Icons.notifications, color: Colors.white),
      ),
    );
  }

  Widget _buildTodayTasks() {
    final todayTasks =
        tasks.where((task) => DateHelper.isToday(task.scheduledDate)).toList();

    if (todayTasks.isEmpty) {
      return _buildEmptyState(
        icon: Icons.today,
        title: 'لا توجد مهام اليوم',
        subtitle: 'استمتع بيومك واستعد للمهام القادمة',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: todayTasks.length,
      itemBuilder: (context, index) {
        final task = todayTasks[index];
        return TaskCard(task: task, onTap: () => _navigateToTaskDetail(task));
      },
    );
  }

  Widget _buildAllTasks() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return TaskCard(task: task, onTap: () => _navigateToTaskDetail(task));
      },
    );
  }

  Widget _buildCompletedTasks() {
    final completedTasks = tasks.where((task) => task.isCompleted).toList();

    if (completedTasks.isEmpty) {
      return _buildEmptyState(
        icon: Icons.check_circle_outline,
        title: 'لا توجد مهام منجزة',
        subtitle: 'ابدأ بإنجاز مهامك اليومية',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: completedTasks.length,
      itemBuilder: (context, index) {
        final task = completedTasks[index];
        return TaskCard(task: task, onTap: () => _navigateToTaskDetail(task));
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToTaskDetail(HajjTask task) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => TaskDetailScreen(
              task: task,
              onTaskUpdated: (updatedTask) {
                setState(() {
                  final index = tasks.indexWhere((t) => t.id == updatedTask.id);
                  if (index != -1) {
                    tasks[index] = updatedTask;
                  }
                });
              },
            ),
      ),
    );
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        // الرئيسية - لا نحتاج لفعل شيء
        break;
      case 1:
        // الترحيب
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => WelcomeScreen(campInfo: HajjTasksData.campInfo),
          ),
        );
        break;
      case 2:
        // حول التطبيق
        _showAboutDialog();
        break;
    }
  }

  void _showTodayReminder() {
    final todayTasks =
        tasks
            .where(
              (task) =>
                  DateHelper.isToday(task.scheduledDate) && !task.isCompleted,
            )
            .toList();

    if (todayTasks.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد مهام متبقية لليوم'),
          backgroundColor: Colors.green,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.today, color: Colors.blue),
                SizedBox(width: 8),
                Text('مهام اليوم'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('لديك ${todayTasks.length} مهمة متبقية اليوم:'),
                const SizedBox(height: 8),
                ...todayTasks.map(
                  (task) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text('• ${task.title}'),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'مهام الحاج اليومية',
      applicationVersion: '1.0.0',
      applicationIcon: Icon(Icons.mosque, size: 48, color: Colors.green[600]),
      children: [
        const Text(
          'تطبيق يساعد الحجاج على متابعة مهامهم اليومية خلال رحلة الحج المباركة.',
        ),
        const SizedBox(height: 16),
        const Text(
          'يحتوي التطبيق على:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const Text(
          '• تذكيرات يومية للمناسك\n'
          '• معلومات المخيم والخدمات\n'
          '• أسئلة شائعة مع إجاباتها\n'
          '• صور توضيحية للمناسك',
        ),
        const SizedBox(height: 16),
        const Text(
          'تقبل الله حجكم وجعله مبروراً',
          style: TextStyle(fontStyle: FontStyle.italic, color: Colors.green),
        ),
      ],
    );
  }
}
