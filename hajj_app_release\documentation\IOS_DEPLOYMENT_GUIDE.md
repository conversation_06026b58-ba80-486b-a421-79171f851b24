# 📱 دليل تحميل التطبيق على الآيفون

## 🎯 الطرق المتاحة

### 🚀 **الطريقة الأولى: التطوير المباشر (الأسرع)**
- مناسبة للاختبار والتطوير
- تحتاج جهاز Mac مع Xcode
- مجانية تماماً

### 📦 **الطريقة الثانية: بناء ملف IPA**
- للتوزيع على عدة أجهزة
- يمكن مشاركتها مع الآخرين
- تحتاج حساب Apple Developer

### 🌐 **الطريقة الثالثة: تطبيق ويب (PWA)**
- الأسهل والأسرع
- يعمل في المتصفح
- لا يحتاج تثبيت

---

## 🚀 الطريقة الأولى: التطوير المباشر

### **المتطلبات:**
- ✅ جهاز Mac مع macOS 10.15 أو أحدث
- ✅ Xcode 12.0 أو أحدث (مجاني من App Store)
- ✅ كابل USB للآيفون
- ✅ آيفون بنظام iOS 11.0 أو أحدث

### **الخطوة 1: تثبيت Xcode**
```bash
# من App Store أو من Terminal
xcode-select --install
```

### **الخطوة 2: إعداد Flutter للـ iOS**
```bash
# تحقق من حالة Flutter
flutter doctor

# إذا كان هناك مشاكل مع iOS
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer
sudo xcodebuild -runFirstLaunch
flutter doctor --android-licenses
```

### **الخطوة 3: إعداد الآيفون**
1. **افتح الإعدادات** في الآيفون
2. اذهب إلى **"عام" → "VPN وإدارة الجهاز"**
3. **فعّل "وضع المطور"** إذا لم يكن مفعلاً
4. **وصّل الآيفون** بالـ Mac عبر USB
5. **اقبل الثقة** عند ظهور الرسالة على الآيفون

### **الخطوة 4: تشغيل التطبيق**
```bash
# في مجلد المشروع
cd /path/to/hajj/project

# تحقق من الأجهزة المتصلة
flutter devices

# تشغيل على الآيفون
flutter run -d ios

# أو تحديد الجهاز بالاسم
flutter run -d "iPhone الخاص بك"
```

---

## 📦 الطريقة الثانية: بناء ملف IPA

### **المتطلبات:**
- ✅ حساب Apple Developer (99$ سنوياً)
- ✅ جهاز Mac مع Xcode
- ✅ شهادة توقيع iOS

### **الخطوة 1: إعداد حساب المطور**
1. **سجّل في** [Apple Developer Program](https://developer.apple.com)
2. **ادفع الرسوم** السنوية (99$)
3. **أنشئ App ID** جديد
4. **أنشئ شهادة توقيع**

### **الخطوة 2: إعداد المشروع**
```bash
# فتح المشروع في Xcode
open ios/Runner.xcworkspace

# في Xcode:
# 1. اختر Team (حساب المطور)
# 2. غيّر Bundle Identifier
# 3. اختر Signing Certificate
```

### **الخطوة 3: بناء التطبيق**
```bash
# بناء للإنتاج
flutter build ios --release

# أو بناء ملف IPA
flutter build ipa --release
```

### **الخطوة 4: التوزيع**
- **TestFlight:** للاختبار الداخلي
- **App Store:** للنشر العام
- **Ad Hoc:** للتوزيع المحدود

---

## 🌐 الطريقة الثالثة: تطبيق ويب (الأسهل)

### **المميزات:**
- ✅ لا يحتاج Mac أو Xcode
- ✅ يعمل على أي جهاز
- ✅ سهل التحديث
- ✅ مجاني تماماً

### **الخطوة 1: بناء تطبيق الويب**
```bash
# في مجلد المشروع
flutter build web --release

# النتيجة ستكون في مجلد build/web
```

### **الخطوة 2: رفع على الاستضافة**
يمكن رفع المجلد على:
- **GitHub Pages** (مجاني)
- **Firebase Hosting** (مجاني)
- **Netlify** (مجاني)
- **Vercel** (مجاني)

### **الخطوة 3: الوصول من الآيفون**
1. **افتح Safari** في الآيفون
2. **اذهب لرابط** التطبيق
3. **اضغط على أيقونة المشاركة** 📤
4. **اختر "إضافة إلى الشاشة الرئيسية"**
5. **سيظهر التطبيق** كأيقونة في الشاشة الرئيسية

---

## 🛠️ إعداد سريع للويب (الطريقة المُوصى بها)

### **خطوات سريعة:**

#### **1. بناء التطبيق:**
```bash
flutter build web --release
```

#### **2. رفع على GitHub Pages:**
```bash
# إنشاء repository جديد في GitHub
# رفع مجلد build/web

# أو استخدام GitHub Actions للنشر التلقائي
```

#### **3. الوصول من الآيفون:**
- افتح الرابط في Safari
- أضف للشاشة الرئيسية
- استخدم كتطبيق عادي

---

## 📋 مقارنة الطرق

| الطريقة | السهولة | التكلفة | الوقت | التوافق |
|---------|---------|---------|-------|---------|
| **التطوير المباشر** | متوسطة | مجاني | سريع | Mac فقط |
| **ملف IPA** | صعبة | 99$ | طويل | Mac فقط |
| **تطبيق ويب** | سهلة جداً | مجاني | سريع جداً | أي جهاز |

---

## 🎯 التوصية

### **للاختبار السريع:**
استخدم **تطبيق الويب** - الأسهل والأسرع

### **للاستخدام الشخصي:**
استخدم **التطوير المباشر** إذا كان لديك Mac

### **للتوزيع التجاري:**
استخدم **ملف IPA** مع حساب المطور

---

## 🚨 ملاحظات مهمة

### **للآيفون:**
- ✅ التطبيق يدعم iOS 11.0 فما فوق
- ✅ يعمل على جميع أحجام الشاشات
- ✅ يدعم الوضع الليلي
- ✅ متوافق مع اللغة العربية

### **للأمان:**
- 🔒 جميع البيانات محفوظة محلياً
- 🔒 لا يتطلب إنترنت للعمل
- 🔒 لا يجمع معلومات شخصية

---

## 📞 المساعدة

### **إذا واجهت مشاكل:**
1. **تأكد من تحديث** Flutter و Xcode
2. **تحقق من اتصال** الآيفون بالكمبيوتر
3. **جرب إعادة تشغيل** Xcode والآيفون
4. **تأكد من تفعيل** وضع المطور

### **للدعم الفني:**
- راجع [وثائق Flutter الرسمية](https://flutter.dev/docs)
- تحقق من [مشاكل iOS الشائعة](https://flutter.dev/docs/deployment/ios)

---

**🎉 مبروك! الآن يمكنك تشغيل التطبيق على الآيفون!**

*اختر الطريقة التي تناسبك وابدأ في استخدام تطبيق المهام اليومية للحج*
