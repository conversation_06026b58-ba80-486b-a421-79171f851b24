# دليل المطور - تطبيق مهام الحاج اليومية

## 🏗️ بنية المشروع

### هيكل المجلدات
```
lib/
├── main.dart                 # نقطة البداية والإعدادات الأساسية
├── models/                   # نماذج البيانات
│   └── hajj_task.dart       # نموذج المهمة ومعلومات المخيم
├── screens/                  # شاشات التطبيق
│   ├── home_screen.dart     # الشاشة الرئيسية مع التبويبات
│   ├── welcome_screen.dart  # الشاشة الترحيبية
│   └── task_detail_screen.dart # شاشة تفاصيل المهمة
├── widgets/                  # مكونات واجهة المستخدم
│   ├── task_card.dart       # بطاقة عرض المهمة
│   └── camp_map.dart        # خارطة المخيم
├── data/                     # بيانات التطبيق
│   └── hajj_tasks_data.dart # بيانات المهام ومعلومات المخيم
├── services/                 # خدمات التطبيق
│   └── app_settings.dart    # إدارة الإعدادات والتخزين المحلي
└── utils/                    # أدوات مساعدة
    ├── date_helper.dart     # مساعد التواريخ والأوقات
    └── app_constants.dart   # ثوابت التطبيق
```

## 🔧 المكونات الأساسية

### 1. نموذج البيانات (HajjTask)
```dart
class HajjTask {
  final String id;              // معرف فريد للمهمة
  final String title;           // عنوان المهمة
  final String description;     // وصف مختصر
  final DateTime scheduledDate; // تاريخ المهمة
  final String timeOfDay;       // وقت اليوم (morning/afternoon/evening)
  final List<String> content;   // محتوى المهمة
  final List<String>? images;   // صور توضيحية (اختيارية)
  final List<String>? faqs;     // أسئلة شائعة (اختيارية)
  final bool isCompleted;       // حالة الإنجاز
  final TaskType type;          // نوع المهمة
}
```

### 2. أنواع المهام (TaskType)
```dart
enum TaskType {
  welcome,      // ترحيب
  ritual,       // مناسك
  prayer,       // دعاء
  celebration,  // احتفال
  faq,          // أسئلة شائعة
  farewell,     // وداع
}
```

### 3. معلومات المخيم (CampInfo)
```dart
class CampInfo {
  final String muftiNumber;                    // رقم المفتي
  final String campName;                       // اسم المخيم
  final Map<String, String> hallNumbers;       // أرقام الصالات
  final Map<String, String> serviceLocations; // مواقع الخدمات
}
```

## 📱 الشاشات الرئيسية

### 1. الشاشة الرئيسية (HomeScreen)
- **التبويبات**: اليوم، جميع المهام، المنجزة
- **التنقل السفلي**: الرئيسية، الترحيب، حول التطبيق
- **زر التنبيهات العائم**: عرض مهام اليوم المتبقية

### 2. الشاشة الترحيبية (WelcomeScreen)
- **معلومات المخيم**: اسم المخيم ورقم المفتي
- **خارطة المخيم**: أرقام الصالات ومواقع الخدمات
- **زر الاتصال**: اتصال مباشر بالمفتي
- **نصائح مهمة**: إرشادات للحجاج

### 3. شاشة تفاصيل المهمة (TaskDetailScreen)
- **معلومات التاريخ والوقت**: عرض منسق للتوقيت
- **المحتوى التفصيلي**: نقاط مرقمة ومنسقة
- **الصور التوضيحية**: عرض الصور المرفقة
- **الأسئلة الشائعة**: أسئلة وإجابات منسقة
- **تسجيل الإنجاز**: إمكانية تسجيل إتمام المهمة

## 🎨 نظام التصميم

### الألوان الأساسية
```dart
// الألوان الرئيسية
primaryColor: Colors.green[600]     // أخضر إسلامي
secondaryColor: Colors.blue[600]    // أزرق
accentColor: Colors.orange[600]     // برتقالي للتنبيهات

// ألوان أنواع المهام
welcome: Colors.blue
ritual: Colors.green
prayer: Colors.purple
celebration: Colors.orange
faq: Colors.teal
farewell: Colors.red
```

### الخطوط والأحجام
```dart
// أحجام النصوص
titleFontSize: 24.0
subtitleFontSize: 18.0
bodyFontSize: 16.0
captionFontSize: 14.0

// المسافات
defaultPadding: 16.0
smallPadding: 8.0
largePadding: 24.0
```

## 🔄 إدارة الحالة

### نظام الإعدادات (AppSettings)
```dart
// حفظ المهام المكتملة
AppSettings.addCompletedTask(taskId)
AppSettings.isTaskCompleted(taskId)

// إعدادات التطبيق
AppSettings.setNotificationsEnabled(true)
AppSettings.setFontSize(18.0)

// معلومات المخيم المخصصة
AppSettings.saveCampInfo(
  muftiNumber: '+966501234567',
  campName: 'مخيم الحج المبارك'
)
```

## 📅 نظام التواريخ

### مساعد التواريخ (DateHelper)
```dart
// تنسيق التواريخ بالعربية
DateHelper.formatArabicDate(date)
DateHelper.getDateDescription(date) // "اليوم"، "غداً"، إلخ

// التحقق من التواريخ
DateHelper.isToday(date)
DateHelper.isTomorrow(date)
DateHelper.isPast(date)

// تحويل أوقات اليوم
DateHelper.getTimeOfDayText('morning') // "صباحاً"
```

## 🛠️ إضافة ميزات جديدة

### إضافة مهمة جديدة
1. افتح `lib/data/hajj_tasks_data.dart`
2. أضف مهمة جديدة في `getTasks()`:
```dart
HajjTask(
  id: 'unique_id',
  title: 'عنوان المهمة',
  description: 'وصف مختصر',
  scheduledDate: targetDate,
  timeOfDay: 'morning', // أو afternoon/evening
  content: [
    'النقطة الأولى',
    'النقطة الثانية',
    // ...
  ],
  faqs: [ // اختياري
    'س: السؤال؟\nج: الإجابة',
  ],
  images: [ // اختياري
    'assets/images/image.jpg',
  ],
  type: TaskType.ritual,
),
```

### إضافة نوع مهمة جديد
1. أضف النوع في `lib/models/hajj_task.dart`:
```dart
enum TaskType {
  // الأنواع الموجودة...
  newType, // النوع الجديد
}
```

2. أضف الدعم في `lib/widgets/task_card.dart`:
```dart
// في _getTaskTypeIcon()
case TaskType.newType:
  return Icons.new_icon;

// في _getTaskTypeColor()
case TaskType.newType:
  return Colors.newColor;

// في _getTaskTypeText()
case TaskType.newType:
  return 'النص العربي';
```

### إضافة شاشة جديدة
1. أنشئ ملف جديد في `lib/screens/`
2. اتبع نمط الشاشات الموجودة:
```dart
class NewScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('العنوان')),
      body: // محتوى الشاشة
    );
  }
}
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
# جميع الاختبارات
flutter test

# اختبار محدد
flutter test test/widget_test.dart

# تحليل الكود
flutter analyze
```

### إضافة اختبار جديد
```dart
testWidgets('وصف الاختبار', (WidgetTester tester) async {
  await tester.pumpWidget(const HajjApp());
  
  // التحقق من وجود عنصر
  expect(find.text('النص المطلوب'), findsOneWidget);
  
  // محاكاة نقرة
  await tester.tap(find.byIcon(Icons.add));
  await tester.pump();
  
  // التحقق من النتيجة
  expect(find.text('النتيجة المتوقعة'), findsOneWidget);
});
```

## 📦 البناء والنشر

### بناء التطبيق
```bash
# بناء للأندرويد
flutter build apk --release

# بناء للـ iOS
flutter build ios --release

# بناء للويب
flutter build web --release
```

### تحسين الأداء
- استخدم `const` للـ widgets الثابتة
- تجنب إعادة البناء غير الضرورية
- استخدم `ListView.builder` للقوائم الطويلة
- ضغط الصور قبل إضافتها

## 🔍 نصائح للتطوير

### أفضل الممارسات
1. **تنظيم الكود**: اتبع بنية المجلدات المحددة
2. **التسمية**: استخدم أسماء واضحة ومعبرة
3. **التعليقات**: أضف تعليقات باللغة العربية للوضوح
4. **الاختبارات**: اكتب اختبارات لكل ميزة جديدة
5. **الأداء**: راقب استهلاك الذاكرة والمعالج

### أدوات مفيدة
- **Flutter Inspector**: لفحص شجرة الـ widgets
- **Dart DevTools**: لمراقبة الأداء
- **Hot Reload**: للتطوير السريع
- **Flutter Doctor**: للتحقق من البيئة

---

**للمساعدة أو الاستفسارات، يرجى فتح issue في المشروع** 🛠️
