# 💻 كيفية تشغيل التطبيق على الكمبيوتر

## 🚀 طريقة سريعة (3 خطوات)

### **الخطوة 1: فتح مجلد التطبيق**
1. **اذهب لمجلد** `web_version`
2. **اضغط Shift + انقر بزر الماوس الأيمن** في المجلد
3. **اختر "فتح نافذة PowerShell هنا"** أو **"Open command window here"**

### **الخطوة 2: تشغيل الخادم**
**اكتب الأمر التالي:**
```
python -m http.server 8000
```

### **الخطوة 3: فتح التطبيق**
1. **افتح المتصفح** (Chrome أو Firefox)
2. **اذهب إلى:** `http://localhost:8000`
3. **استمتع بالتطبيق!**

---

## 📱 لمحاكاة الآيفون

### **في Chrome:**
1. **اضغط F12**
2. **اضغط على أيقونة الهاتف** 📱
3. **اختر "iPhone"** من القائمة
4. **أعد تحميل الصفحة**

---

## 🔧 إذا لم يعمل Python

### **تحميل Python:**
1. **اذهب إلى** [python.org](https://python.org)
2. **حمّل أحدث إصدار**
3. **ثبّته مع تفعيل "Add to PATH"**

### **طريقة بديلة:**
1. **انقر مرتين** على ملف `index.html` في مجلد `web_version`
2. **سيفتح في المتصفح** (قد لا تعمل جميع الميزات)

---

## ✅ النتيجة

ستحصل على:
- ✅ **تطبيق كامل** يعمل في المتصفح
- ✅ **جميع ميزات الحج** متاحة
- ✅ **واجهة عربية** احترافية
- ✅ **حفظ تلقائي** للإعدادات

---

**🎉 مبروك! التطبيق يعمل الآن على الكمبيوتر**

*للإيقاف: اضغط Ctrl+C في نافذة الأوامر*
