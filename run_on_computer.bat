@echo off
echo ========================================
echo     تشغيل تطبيق المهام اليومية للحج
echo           على الكمبيوتر
echo ========================================
echo.

set WEB_DIR=hajj_app_release\web_version

echo [1/3] التحقق من وجود الملفات...
if not exist "%WEB_DIR%" (
    echo ❌ مجلد التطبيق غير موجود!
    echo.
    echo 💡 تأكد من:
    echo 1. استخراج الملف المضغوط أولاً
    echo 2. وجود مجلد hajj_app_release
    echo 3. وجود مجلد web_version بداخله
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملفات التطبيق

echo.
echo [2/3] اختر طريقة التشغيل:
echo.
echo 1. Python HTTP Server (الأسهل)
echo 2. فتح مباشر في المتصفح
echo 3. Node.js serve
echo 4. إلغاء
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto python_server
if "%choice%"=="2" goto direct_open
if "%choice%"=="3" goto nodejs_serve
if "%choice%"=="4" goto end
goto invalid_choice

:python_server
echo.
echo [3/3] تشغيل خادم Python...
echo.
echo 🌐 سيفتح التطبيق على: http://localhost:8000
echo 🔄 للإيقاف: اضغط Ctrl+C
echo.
cd "%WEB_DIR%"
python -m http.server 8000
goto end

:direct_open
echo.
echo [3/3] فتح في المتصفح...
start "" "%WEB_DIR%\index.html"
echo.
echo ✅ تم فتح التطبيق في المتصفح
echo.
echo ⚠️ ملاحظة: قد لا تعمل جميع الميزات بهذه الطريقة
echo 💡 للحصول على أفضل تجربة، استخدم الخادم المحلي
goto end

:nodejs_serve
echo.
echo [3/3] تشغيل خادم Node.js...
echo.
echo 🔍 التحقق من وجود serve...
where serve >nul 2>nul
if errorlevel 1 (
    echo ❌ serve غير مثبت
    echo.
    echo 💡 لتثبيته:
    echo npm install -g serve
    echo.
    pause
    goto end
)

echo 🌐 تشغيل الخادم...
cd "%WEB_DIR%"
serve .
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح
goto end

:end
echo.
echo 📱 نصائح للاستخدام:
echo - استخدم Chrome أو Firefox للحصول على أفضل تجربة
echo - اضغط F12 وفعّل وضع الهاتف لمحاكاة الآيفون
echo - جميع الإعدادات تُحفظ في المتصفح
echo.
pause
