@echo off
echo ========================================
echo      إنشاء ملف التحميل المضغوط
echo    تطبيق المهام اليومية للحج
echo        حملة الفرقان
echo ========================================
echo.

set RELEASE_DIR=hajj_app_release
set ZIP_NAME=تطبيق_المهام_اليومية_للحج_v1.0.zip

echo [1/3] التحقق من وجود المجلد...
if not exist %RELEASE_DIR% (
    echo ❌ المجلد غير موجود! يرجى تشغيل create_release_package.bat أولاً
    pause
    exit /b 1
)

echo [2/3] إنشاء ملف README للتحميل...
(
echo # 📱 تطبيق المهام اليومية للحج - حملة الفرقان
echo.
echo ## 🎉 مرحباً بك في التطبيق الشامل للحج
echo.
echo ### 📦 ما تحصل عليه:
echo - ✅ تطبيق ويب كامل يعمل على الآيفون
echo - ✅ جميع الأدلة والوثائق
echo - ✅ تعليمات مفصلة خطوة بخطوة
echo - ✅ دعم كامل للغة العربية
echo.
echo ### 🚀 للبدء السريع:
echo 1. **استخرج الملفات** من هذا الأرشيف
echo 2. **ارفع مجلد web_version** على استضافة مجانية
echo 3. **افتح الرابط في Safari** على الآيفون
echo 4. **أضف للشاشة الرئيسية**
echo.
echo ### 📁 محتويات الحزمة:
echo - **web_version/** - التطبيق الجاهز للنشر
echo - **documentation/** - جميع الأدلة والوثائق
echo - **اقرأني_أولاً.md** - دليل البداية
echo - **تعليمات_سريعة.md** - خطوات سريعة
echo - **معلومات_الإصدار.md** - تفاصيل الإصدار
echo.
echo ### 🌐 استضافات مجانية مُوصى بها:
echo - **Netlify.com** - الأسهل ^(اسحب وأفلت^)
echo - **GitHub Pages** - مجاني للأبد
echo - **Firebase Hosting** - من Google
echo - **Vercel** - سريع جداً
echo.
echo ### ✨ ميزات التطبيق:
echo - 📅 **8 مهام يومية** مفصلة للحج ^(أيام 8-13 ذي الحجة^)
echo - 🗺️ **خارطة مخيم** قابلة للتعديل والتخصيص
echo - 📞 **رقم مفتي** قابل للتغيير والحفظ
echo - 🎨 **واجهة عربية** احترافية وجميلة
echo - 📱 **يعمل على الآيفون** كتطبيق حقيقي
echo - 💾 **حفظ تلقائي** لجميع الإعدادات
echo - 🔄 **عمل بدون إنترنت** بعد التحميل الأول
echo.
echo ### 📱 متطلبات النظام:
echo - **آيفون:** iOS 11.0 أو أحدث
echo - **أندرويد:** Android 5.0 أو أحدث
echo - **متصفح:** Safari ^(للآيفون^) أو Chrome ^(للأندرويد^)
echo.
echo ### 🎯 خطوات سريعة للآيفون:
echo 1. **ارفع مجلد web_version** على netlify.com
echo 2. **انسخ الرابط** الذي تحصل عليه
echo 3. **افتح الرابط في Safari** على الآيفون
echo 4. **اضغط أيقونة المشاركة** 📤
echo 5. **اختر "إضافة إلى الشاشة الرئيسية"**
echo 6. **اكتب الاسم:** "المهام اليومية للحج"
echo 7. **اضغط "إضافة"**
echo.
echo ### 🎉 النتيجة:
echo ✅ **أيقونة تطبيق** في الشاشة الرئيسية
echo ✅ **يعمل مثل تطبيق حقيقي** بدون شريط متصفح
echo ✅ **جميع الميزات متاحة** وتعمل بسلاسة
echo ✅ **سرعة عالية** وأداء ممتاز
echo.
echo ### 📞 الدعم:
echo - راجع ملف **"اقرأني_أولاً.md"** للتفاصيل الكاملة
echo - راجع مجلد **"documentation"** للأدلة المفصلة
echo - جرب **"تعليمات_سريعة.md"** للبدء السريع
echo.
echo ---
echo.
echo **تقبل الله حجكم وجعله مبروراً** 🕋
echo.
echo *تم تطوير هذا التطبيق بعناية لخدمة ضيوف الرحمن*
echo.
echo **الإصدار:** 1.0.0  
echo **التاريخ:** %date%  
echo **التقنية:** Flutter Web + PWA
) > "%RELEASE_DIR%\README_للتحميل.md"

echo [3/3] إنشاء ملف مضغوط...
echo.
echo 📦 جاري إنشاء الملف المضغوط...
echo    هذا قد يستغرق بضع ثوانٍ...
echo.

REM محاولة استخدام PowerShell لإنشاء ZIP
powershell -command "Compress-Archive -Path '%RELEASE_DIR%\*' -DestinationPath '%ZIP_NAME%' -Force" 2>nul

if exist "%ZIP_NAME%" (
    echo ✅ تم إنشاء الملف المضغوط بنجاح!
    echo.
    echo ========================================
    echo           تم الانتهاء بنجاح!
    echo ========================================
    echo.
    echo 📁 **المجلد:** %RELEASE_DIR%
    echo 📦 **الملف المضغوط:** %ZIP_NAME%
    echo.
    echo 🎯 **للاستخدام:**
    echo 1. شارك الملف المضغوط مع المستخدمين
    echo 2. يستخرجون الملفات
    echo 3. يرفعون مجلد web_version على استضافة
    echo 4. يستخدمون التطبيق على الآيفون
    echo.
    echo 📊 **حجم الملف:** 
    for %%A in ("%ZIP_NAME%") do echo    %%~zA bytes
    echo.
    echo 🚀 **جاهز للتوزيع والتحميل!**
) else (
    echo ❌ فشل في إنشاء الملف المضغوط
    echo.
    echo 💡 يمكنك ضغط مجلد "%RELEASE_DIR%" يدوياً:
    echo 1. انقر بزر الماوس الأيمن على المجلد
    echo 2. اختر "إرسال إلى" ← "مجلد مضغوط"
    echo 3. أعد تسمية الملف إلى "%ZIP_NAME%"
)

echo.
echo 📚 **ملفات مهمة:**
echo - اقرأني_أولاً.md - دليل البداية
echo - تعليمات_سريعة.md - خطوات سريعة  
echo - web_version/ - التطبيق الجاهز
echo - documentation/ - جميع الأدلة
echo.
pause
