@echo off
echo ========================================
echo      إنشاء حزمة التطبيق للآيفون
echo    تطبيق المهام اليومية للحج
echo        حملة الفرقان 1446
echo ========================================
echo.

set IPHONE_DIR=hajj_iphone_app
set WEB_DIR=%IPHONE_DIR%\web_app
set DOCS_DIR=%IPHONE_DIR%\guides

echo [1/6] إنشاء مجلدات الآيفون...
if exist %IPHONE_DIR% rmdir /s /q %IPHONE_DIR%
mkdir %IPHONE_DIR%
mkdir %WEB_DIR%
mkdir %DOCS_DIR%

echo [2/6] نسخ تطبيق الويب للآيفون...
xcopy "hajj_app_release\web_version\*" "%WEB_DIR%\" /E /I /Y

echo [3/6] نسخ الأدلة الخاصة بالآيفون...
copy "QUICK_IPHONE_SETUP.md" "%DOCS_DIR%\" 2>nul
copy "IOS_DEPLOYMENT_GUIDE.md" "%DOCS_DIR%\" 2>nul
copy "hajj_app_release\كيفية_إضافة_التطبيق_للشاشة_الرئيسية.md" "%DOCS_DIR%\" 2>nul
copy "MOBILE_APP_GUIDE.md" "%DOCS_DIR%\" 2>nul

echo [4/6] إنشاء دليل الآيفون الرئيسي...
(
echo # 📱 تطبيق المهام اليومية للحج - للآيفون
echo.
echo ## 🎉 مرحباً بك في التطبيق المخصص للآيفون
echo.
echo ### 📁 محتويات الحزمة:
echo - **web_app/** - التطبيق الجاهز للنشر على الإنترنت
echo - **guides/** - أدلة مخصصة للآيفون
echo - **iPhone_Setup_Guide.md** - دليل التثبيت السريع
echo.
echo ### 🚀 للبدء السريع ^(3 خطوات^):
echo.
echo #### الخطوة 1: رفع التطبيق
echo 1. اذهب إلى **netlify.com**
echo 2. اسحب مجلد **web_app** إلى الموقع
echo 3. انسخ الرابط الذي يظهر لك
echo.
echo #### الخطوة 2: فتح على الآيفون
echo 1. افتح **Safari** في الآيفون
echo 2. اذهب للرابط الذي نسخته
echo 3. انتظر حتى يحمّل التطبيق
echo.
echo #### الخطوة 3: إضافة للشاشة الرئيسية
echo 1. اضغط أيقونة **المشاركة** 📤
echo 2. اختر **"إضافة إلى الشاشة الرئيسية"**
echo 3. اكتب الاسم: **"مهام الحج"**
echo 4. اضغط **"إضافة"**
echo.
echo ### ✨ النتيجة:
echo - ✅ **أيقونة جميلة** برمز الكعبة في الشاشة الرئيسية
echo - ✅ **يعمل مثل تطبيق حقيقي** بدون شريط متصفح
echo - ✅ **جميع ميزات الحج** متاحة ^(حج 1446 هـ^)
echo - ✅ **يعمل بدون إنترنت** بعد التحميل الأول
echo.
echo ### 🎯 ميزات التطبيق:
echo - 📅 **8 مهام يومية** مفصلة للحج ^(8-13 ذو الحجة 1446^)
echo - 🗺️ **خارطة مخيم** قابلة للتعديل والتخصيص
echo - 📞 **رقم مفتي الحملة** قابل للتغيير
echo - 🎨 **واجهة عربية** احترافية وجميلة
echo - 🕋 **شعار حملة الفرقان** ^(عند إضافة الصورة^)
echo.
echo ### 🌐 استضافات مجانية مُوصى بها:
echo - **Netlify.com** - الأسهل ^(اسحب وأفلت^)
echo - **GitHub Pages** - مجاني للأبد
echo - **Firebase Hosting** - من Google
echo - **Vercel** - سريع جداً
echo.
echo ### 📚 الأدلة المتاحة:
echo - **iPhone_Setup_Guide.md** - دليل مفصل للآيفون
echo - **guides/QUICK_IPHONE_SETUP.md** - دليل سريع
echo - **guides/كيفية_إضافة_التطبيق_للشاشة_الرئيسية.md** - دليل مصور
echo.
echo ### 🔧 للمطورين:
echo - **guides/IOS_DEPLOYMENT_GUIDE.md** - دليل النشر الشامل
echo - **guides/MOBILE_APP_GUIDE.md** - دليل التطبيقات المحمولة
echo.
echo ---
echo.
echo **🎉 مبروك! التطبيق جاهز للاستخدام على الآيفون**
echo.
echo *تقبل الله حجكم وجعله مبروراً* 🕋
) > "%IPHONE_DIR%\README_iPhone.md"

echo [5/6] إنشاء دليل التثبيت السريع للآيفون...
(
echo # 📱 دليل التثبيت السريع للآيفون
echo.
echo ## 🎯 الهدف: الحصول على تطبيق المهام اليومية للحج على الآيفون
echo.
echo ---
echo.
echo ## 🚀 الطريقة السريعة ^(5 دقائق^)
echo.
echo ### **الخطوة 1: رفع التطبيق على الإنترنت**
echo.
echo #### أ^) استخدام Netlify ^(الأسهل^):
echo 1. **اذهب إلى** [netlify.com](https://netlify.com)
echo 2. **اسحب مجلد web_app** إلى الموقع
echo 3. **انتظر حتى يكتمل الرفع**
echo 4. **انسخ الرابط** الذي يظهر لك
echo.
echo #### ب^) استخدام GitHub Pages:
echo 1. **أنشئ حساب** في [github.com](https://github.com)
echo 2. **أنشئ repository جديد**
echo 3. **ارفع محتويات web_app**
echo 4. **فعّل GitHub Pages**
echo 5. **احصل على الرابط**
echo.
echo ### **الخطوة 2: تثبيت على الآيفون**
echo.
echo #### في الآيفون:
echo 1. **افتح Safari** ^(مهم: استخدم Safari وليس Chrome^)
echo 2. **اذهب للرابط** الذي حصلت عليه
echo 3. **انتظر حتى يحمّل التطبيق** بالكامل
echo 4. **اضغط أيقونة المشاركة** 📤 ^(أسفل الشاشة^)
echo 5. **مرر لأسفل** في قائمة الخيارات
echo 6. **اضغط "إضافة إلى الشاشة الرئيسية"**
echo 7. **اكتب الاسم:** "مهام الحج"
echo 8. **اضغط "إضافة"**
echo.
echo ### **النتيجة:**
echo - ✅ **أيقونة جميلة** مع رمز الكعبة
echo - ✅ **اسم "مهام الحج"** تحت الأيقونة
echo - ✅ **يفتح مثل تطبيق حقيقي**
echo - ✅ **يعمل بدون إنترنت**
echo.
echo ---
echo.
echo ## 🎨 ما ستحصل عليه:
echo.
echo ### **📅 المهام اليومية لحج 1446 هـ:**
echo - **اليوم 8:** يوم التروية - الذهاب إلى منى
echo - **اليوم 9:** يوم عرفة - الوقوف بعرفة
echo - **اليوم 10:** يوم النحر - رمي الجمرات والنحر
echo - **الأيام 11-13:** أيام التشريق ورمي الجمرات
echo.
echo ### **🗺️ خارطة المخيم التفاعلية:**
echo - **أرقام الصالات** قابلة للتعديل
echo - **مواقع الخدمات** قابلة للتخصيص
echo - **حفظ تلقائي** للتغييرات
echo.
echo ### **📞 معلومات الاتصال:**
echo - **رقم مفتي الحملة** قابل للتغيير
echo - **إمكانية الاتصال المباشر**
echo.
echo ---
echo.
echo ## 🚨 حل المشاكل الشائعة
echo.
echo ### **❌ لا أرى خيار "إضافة للشاشة الرئيسية":**
echo - **تأكد من استخدام Safari** وليس Chrome
echo - **تأكد من تحميل الصفحة** بالكامل أولاً
echo - **جرب إعادة تحميل الصفحة**
echo.
echo ### **❌ التطبيق يفتح في المتصفح:**
echo - **احذف الأيقونة** وأعد إضافتها
echo - **تأكد من اتباع الخطوات** بالترتيب الصحيح
echo.
echo ### **❌ الأيقونة لا تظهر:**
echo - **انتظر قليلاً** حتى تحمّل الأيقونة
echo - **أعد تشغيل الآيفون** إذا لزم الأمر
echo.
echo ---
echo.
echo **🎉 مبروك! الآن لديك تطبيق المهام اليومية للحج على الآيفون**
echo.
echo *استمتع بالتطبيق وادع لمن ساهم في تطويره* 📱✨
) > "%IPHONE_DIR%\iPhone_Setup_Guide.md"

echo [6/6] إنشاء ملف مضغوط للآيفون...
echo.
echo 📦 جاري إنشاء الملف المضغوط للآيفون...

powershell -command "Compress-Archive -Path '%IPHONE_DIR%\*' -DestinationPath 'تطبيق_الحج_للآيفون_1446.zip' -Force" 2>nul

if exist "تطبيق_الحج_للآيفون_1446.zip" (
    echo ✅ تم إنشاء حزمة الآيفون بنجاح!
    echo.
    echo ========================================
    echo        حزمة الآيفون جاهزة!
    echo ========================================
    echo.
    echo 📁 **المجلد:** %IPHONE_DIR%
    echo 📦 **الملف المضغوط:** تطبيق_الحج_للآيفون_1446.zip
    echo.
    echo 📊 **حجم الملف:** 
    for %%A in ("تطبيق_الحج_للآيفون_1446.zip") do echo    %%~zA bytes
    echo.
    echo 🎯 **للاستخدام:**
    echo 1. حمّل الملف المضغوط
    echo 2. استخرج المحتويات
    echo 3. ارفع مجلد web_app على netlify.com
    echo 4. افتح الرابط في Safari على الآيفون
    echo 5. أضف للشاشة الرئيسية
    echo.
    echo 📱 **النتيجة:** تطبيق كامل على الآيفون!
) else (
    echo ❌ فشل في إنشاء الملف المضغوط
    echo.
    echo 💡 يمكنك ضغط مجلد "%IPHONE_DIR%" يدوياً
)

echo.
echo 📚 **الملفات المهمة:**
echo - README_iPhone.md - دليل البداية
echo - iPhone_Setup_Guide.md - دليل التثبيت المفصل
echo - web_app/ - التطبيق الجاهز للنشر
echo - guides/ - أدلة إضافية
echo.
pause
