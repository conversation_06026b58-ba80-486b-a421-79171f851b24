@echo off
echo ========================================
echo      بناء تطبيق أندرويد APK
echo    تطبيق المهام اليومية للحج
echo        حملة الفرقان
echo ========================================
echo.

echo [1/5] التحقق من متطلبات البناء...

REM التحقق من Flutter
flutter --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Flutter غير مثبت أو غير موجود في PATH
    echo.
    echo 💡 لتثبيت Flutter:
    echo 1. اذهب إلى https://flutter.dev
    echo 2. حمّل Flutter SDK
    echo 3. أضفه إلى PATH
    echo.
    pause
    exit /b 1
)

echo ✅ Flutter متوفر

REM التحقق من Android SDK
if not exist "%ANDROID_HOME%" (
    echo ⚠️ Android SDK غير مكون
    echo.
    echo 💡 لتكوين Android SDK:
    echo 1. ثبّت Android Studio
    echo 2. اضبط متغير ANDROID_HOME
    echo 3. أضف Android SDK إلى PATH
    echo.
    echo 🔄 هل تريد المتابعة بدون Android SDK؟ (y/n)
    set /p continue="اختر: "
    if not "%continue%"=="y" if not "%continue%"=="Y" exit /b 1
)

echo [2/5] تنظيف المشروع...
flutter clean

echo [3/5] تحديث التبعيات...
flutter pub get

echo [4/5] بناء APK للإنتاج...
echo.
echo 🔨 جاري بناء APK...
echo    هذا قد يستغرق عدة دقائق...
echo.

flutter build apk --release

if errorlevel 1 (
    echo ❌ فشل في بناء APK
    echo.
    echo 💡 الأسباب المحتملة:
    echo • Android SDK غير مكون بشكل صحيح
    echo • مشاكل في التبعيات
    echo • أخطاء في الكود
    echo.
    echo 🔄 جرب الحلول التالية:
    echo 1. flutter doctor للتحقق من الإعداد
    echo 2. flutter clean && flutter pub get
    echo 3. تأكد من تثبيت Android Studio
    echo.
    pause
    exit /b 1
)

echo [5/5] نسخ APK إلى مجلد الإصدار...

set APK_SOURCE=build\app\outputs\flutter-apk\app-release.apk
set APK_DEST=hajj_app_release\تطبيق_المهام_اليومية_للحج.apk

if exist "%APK_SOURCE%" (
    copy "%APK_SOURCE%" "%APK_DEST%" >nul
    echo ✅ تم نسخ APK بنجاح!
) else (
    echo ❌ لم يتم العثور على ملف APK
    echo.
    echo 📁 ابحث عن الملف في: %APK_SOURCE%
)

echo.
echo ========================================
echo        تم بناء APK بنجاح!
echo ========================================
echo.

if exist "%APK_DEST%" (
    echo 📱 ملف APK: %APK_DEST%
    echo.
    echo 📊 معلومات الملف:
    for %%A in ("%APK_DEST%") do (
        echo    الحجم: %%~zA bytes
        echo    التاريخ: %%~tA
    )
    echo.
    echo 🚀 للتثبيت على الأندرويد:
    echo 1. انقل الملف إلى الهاتف
    echo 2. فعّل "مصادر غير معروفة" في الإعدادات
    echo 3. انقر على ملف APK لتثبيته
    echo.
    echo 📦 للتوزيع:
    echo • شارك ملف APK مع المستخدمين
    echo • أو ارفعه على Google Play Store
    echo.
    echo 🎉 مبروك! تطبيق أندرويد جاهز
) else (
    echo 📁 ابحث عن ملف APK في:
    echo    build\app\outputs\flutter-apk\app-release.apk
)

echo.
echo 💡 نصائح إضافية:
echo • لبناء AAB للـ Play Store: flutter build appbundle
echo • لاختبار APK: flutter install
echo • لتوقيع APK: راجع وثائق Flutter
echo.
pause
