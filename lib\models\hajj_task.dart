class HajjTask {
  final String id;
  final String title;
  final String description;
  final DateTime scheduledDate;
  final String timeOfDay; // 'morning' or 'evening'
  final List<String> content;
  final List<String>? images;
  final List<String>? faqs;
  final bool isCompleted;
  final TaskType type;

  HajjTask({
    required this.id,
    required this.title,
    required this.description,
    required this.scheduledDate,
    required this.timeOfDay,
    required this.content,
    this.images,
    this.faqs,
    this.isCompleted = false,
    required this.type,
  });

  HajjTask copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? scheduledDate,
    String? timeOfDay,
    List<String>? content,
    List<String>? images,
    List<String>? faqs,
    bool? isCompleted,
    TaskType? type,
  }) {
    return HajjTask(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      timeOfDay: timeOfDay ?? this.timeOfDay,
      content: content ?? this.content,
      images: images ?? this.images,
      faqs: faqs ?? this.faqs,
      isCompleted: isCompleted ?? this.isCompleted,
      type: type ?? this.type,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'scheduledDate': scheduledDate.toIso8601String(),
      'timeOfDay': timeOfDay,
      'content': content,
      'images': images,
      'faqs': faqs,
      'isCompleted': isCompleted,
      'type': type.toString(),
    };
  }

  factory HajjTask.fromJson(Map<String, dynamic> json) {
    return HajjTask(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      scheduledDate: DateTime.parse(json['scheduledDate']),
      timeOfDay: json['timeOfDay'],
      content: List<String>.from(json['content']),
      images: json['images'] != null ? List<String>.from(json['images']) : null,
      faqs: json['faqs'] != null ? List<String>.from(json['faqs']) : null,
      isCompleted: json['isCompleted'] ?? false,
      type: TaskType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => TaskType.ritual,
      ),
    );
  }
}

enum TaskType { welcome, ritual, prayer, celebration, faq, farewell }

class CampInfo {
  final String muftiNumber;
  final String campName;
  final Map<String, String> hallNumbers;
  final Map<String, String> serviceLocations;

  CampInfo({
    required this.muftiNumber,
    required this.campName,
    required this.hallNumbers,
    required this.serviceLocations,
  });

  CampInfo copyWith({
    String? muftiNumber,
    String? campName,
    Map<String, String>? hallNumbers,
    Map<String, String>? serviceLocations,
  }) {
    return CampInfo(
      muftiNumber: muftiNumber ?? this.muftiNumber,
      campName: campName ?? this.campName,
      hallNumbers: hallNumbers ?? this.hallNumbers,
      serviceLocations: serviceLocations ?? this.serviceLocations,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'muftiNumber': muftiNumber,
      'campName': campName,
      'hallNumbers': hallNumbers,
      'serviceLocations': serviceLocations,
    };
  }

  factory CampInfo.fromJson(Map<String, dynamic> json) {
    return CampInfo(
      muftiNumber: json['muftiNumber'] ?? '',
      campName: json['campName'] ?? '',
      hallNumbers: Map<String, String>.from(json['hallNumbers'] ?? {}),
      serviceLocations: Map<String, String>.from(
        json['serviceLocations'] ?? {},
      ),
    );
  }
}
