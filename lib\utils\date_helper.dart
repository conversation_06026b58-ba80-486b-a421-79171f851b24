import 'package:intl/intl.dart';

class DateHelper {
  static final DateFormat _arabicDateFormat = DateFormat('EEEE، d MMMM yyyy', 'ar');
  static final DateFormat _timeFormat = DateFormat('HH:mm', 'ar');

  /// تنسيق التاريخ بالعربية
  static String formatArabicDate(DateTime date) {
    return _arabicDateFormat.format(date);
  }

  /// تنسيق الوقت بالعربية
  static String formatTime(DateTime time) {
    return _timeFormat.format(time);
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// التحقق من كون التاريخ غداً
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year &&
        date.month == tomorrow.month &&
        date.day == tomorrow.day;
  }

  /// التحقق من كون التاريخ في الماضي
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// التحقق من كون التاريخ في المستقبل
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// الحصول على نص وصفي للتاريخ
  static String getDateDescription(DateTime date) {
    if (isToday(date)) {
      return 'اليوم';
    } else if (isTomorrow(date)) {
      return 'غداً';
    } else if (isPast(date)) {
      return 'منتهي';
    } else {
      final daysUntil = date.difference(DateTime.now()).inDays;
      if (daysUntil == 1) {
        return 'غداً';
      } else if (daysUntil < 7) {
        return 'خلال $daysUntil أيام';
      } else {
        return formatArabicDate(date);
      }
    }
  }

  /// تحويل وقت اليوم إلى نص عربي
  static String getTimeOfDayText(String timeOfDay) {
    switch (timeOfDay.toLowerCase()) {
      case 'morning':
        return 'صباحاً';
      case 'evening':
        return 'مساءً';
      case 'afternoon':
        return 'بعد الظهر';
      case 'night':
        return 'ليلاً';
      default:
        return timeOfDay;
    }
  }

  /// الحصول على أيام الحج بالترتيب
  static List<String> getHajjDays() {
    return [
      'يوم التروية (8 ذي الحجة)',
      'يوم عرفة (9 ذي الحجة)',
      'يوم النحر (10 ذي الحجة)',
      'أول أيام التشريق (11 ذي الحجة)',
      'ثاني أيام التشريق (12 ذي الحجة)',
      'ثالث أيام التشريق (13 ذي الحجة)',
    ];
  }

  /// تحديد يوم الحج بناءً على التاريخ
  static String getHajjDayName(DateTime date, DateTime hajjStartDate) {
    final daysDifference = date.difference(hajjStartDate).inDays;
    
    switch (daysDifference) {
      case 7:
        return 'يوم التروية';
      case 8:
        return 'يوم عرفة';
      case 9:
        return 'يوم النحر';
      case 10:
        return 'أول أيام التشريق';
      case 11:
        return 'ثاني أيام التشريق';
      case 12:
        return 'ثالث أيام التشريق';
      default:
        return formatArabicDate(date);
    }
  }
}
