# 🕋 نظام إدارة وجبات الحج - Hajj Meals Management System

## 📋 **الوصف - Description**

### العربية:
نظام شامل لإدارة وجبات الحجاج في المشاعر المقدسة، يوفر واجهة عصرية وتفاعلية لمراقبة وإدارة الوجبات والقاعات في الوقت الفعلي.

### English:
A comprehensive system for managing pilgrims' meals in the holy sites, providing a modern and interactive interface for real-time monitoring and management of meals and halls.

---

## ✨ **المميزات - Features**

### 🌐 **دعم اللغات - Language Support**
- ✅ دعم كامل للغة العربية (RTL)
- ✅ دعم كامل للغة الإنجليزية (LTR)
- ✅ تبديل فوري بين اللغات

### ⏰ **التوقيت - Timing**
- ✅ عرض الوقت والتاريخ حسب توقيت مكة المكرمة (UTC+3)
- ✅ تحديث مباشر كل ثانية
- ✅ تنسيق محلي للتاريخ والوقت

### 🍽️ **إدارة الوجبات - Meals Management**
- ✅ إدارة ثلاث وجبات (إفطار - غداء - عشاء)
- ✅ تعديل أوقات الوجبات
- ✅ تعديل مدة الوجبات (30-300 دقيقة)
- ✅ تفعيل/إلغاء تفعيل الوجبات

### 🏛️ **إدارة القاعات - Halls Management**
- ✅ مراقبة 12 قاعة
- ✅ عرض السعة والعدد الحالي
- ✅ أربع حالات: نشطة، انتظار، مكتملة، في الاستعداد
- ✅ فلترة القاعات حسب الحالة
- ✅ تغيير حالة القاعات بالنقر

### 🔔 **نظام التنبيهات - Notifications System**
- ✅ تنبيهات مباشرة للأحداث
- ✅ ثلاثة أنواع: نجاح، تحذير، خطأ
- ✅ إخفاء تلقائي بعد 5 ثوان

### 📊 **الإحصائيات - Statistics**
- ✅ إجمالي القاعات
- ✅ القاعات النشطة
- ✅ الوجبات المقدمة
- ✅ السعة الإجمالية

### 🎨 **التصميم - Design**
- ✅ واجهة عصرية وجذابة
- ✅ تدرجات لونية متقدمة
- ✅ تأثيرات بصرية متحركة
- ✅ تصميم متجاوب (Responsive)

---

## 🚀 **طريقة التشغيل - How to Run**

### **الطريقة الأولى - Method 1:**
1. **انقر مرتين** على ملف `run_meals_system.bat`
2. **سيفتح التطبيق** في المتصفح تلقائياً

### **الطريقة الثانية - Method 2:**
1. **انقر مرتين** على ملف `hajj_meals_management.html`
2. **سيفتح التطبيق** في المتصفح الافتراضي

### **الطريقة الثالثة - Method 3:**
1. **افتح المتصفح** (Chrome, Firefox, Safari, Edge)
2. **اسحب الملف** `hajj_meals_management.html` إلى المتصفح

---

## 🎯 **طريقة الاستخدام - How to Use**

### **تبديل اللغة - Language Toggle:**
- **اضغط زر** "English" أو "العربية" في أعلى الصفحة

### **إدارة الوجبات - Meals Management:**
- **اضغط على بطاقة الوجبة** لتفعيل/إلغاء تفعيل
- **عدّل الوقت** باستخدام حقل الوقت
- **عدّل المدة** باستخدام حقل الرقم

### **فلترة القاعات - Filter Halls:**
- **اضغط على أزرار الفلترة:** الكل، نشطة، انتظار، مكتملة، في الاستعداد

### **تغيير حالة القاعة - Change Hall Status:**
- **اضغط على بطاقة القاعة** للتنقل بين الحالات

---

## 🔧 **المتطلبات التقنية - Technical Requirements**

### **المتصفحات المدعومة - Supported Browsers:**
- ✅ Google Chrome (الموصى به - Recommended)
- ✅ Mozilla Firefox
- ✅ Microsoft Edge
- ✅ Safari
- ✅ Opera

### **المتطلبات - Requirements:**
- ✅ لا يحتاج إنترنت (يعمل محلياً)
- ✅ لا يحتاج خادم
- ✅ لا يحتاج تثبيت
- ✅ JavaScript مفعل في المتصفح

---

## 📱 **التوافق - Compatibility**

### **الأجهزة - Devices:**
- ✅ أجهزة الكمبيوتر (Windows, Mac, Linux)
- ✅ الأجهزة اللوحية (iPad, Android tablets)
- ✅ الهواتف الذكية (iPhone, Android)

### **الدقة - Resolution:**
- ✅ تصميم متجاوب لجميع الأحجام
- ✅ دعم الشاشات الصغيرة والكبيرة

---

## 🛠️ **التخصيص - Customization**

### **يمكن تعديل - Can be Modified:**
- ✅ الألوان والتدرجات
- ✅ عدد القاعات
- ✅ أنواع الوجبات
- ✅ النصوص والترجمات
- ✅ التوقيت والمنطقة الزمنية

---

## 📞 **الدعم - Support**

### **للمساعدة - For Help:**
- 📧 **البريد الإلكتروني:** <EMAIL>
- 📱 **الهاتف:** +966-XX-XXX-XXXX
- 🌐 **الموقع:** www.hajjmeals.com

---

## 📄 **الترخيص - License**

هذا التطبيق مجاني للاستخدام في أغراض الحج والعمرة.
This application is free to use for Hajj and Umrah purposes.

---

## 🎉 **شكر وتقدير - Acknowledgments**

تم تطوير هذا النظام لخدمة ضيوف الرحمن في المشاعر المقدسة.
This system was developed to serve the guests of Allah in the holy sites.

**🕋 تقبل الله حجكم وسعيكم**
**🕋 May Allah accept your Hajj and efforts**
