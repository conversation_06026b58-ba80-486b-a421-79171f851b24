# 🎨 إضافة شعار حملة الفرقان

## الخطوات المطلوبة لإضافة الشعار:

### 1. حفظ ملف الشعار
- احفظ الصورة التي أرسلتها باسم `forgan_logo.png`
- ضعها في المجلد: `assets/logos/`
- تأكد من أن الصورة بصيغة PNG وبجودة عالية

### 2. تحديث ملف pubspec.yaml
تأكد من وجود هذا السطر في قسم assets:
```yaml
flutter:
  assets:
    - assets/logos/
```

### 3. استبدال الأيقونة المؤقتة بالشعار الفعلي

#### في الشاشة الرئيسية (lib/screens/home_screen.dart):
استبدل هذا الكود:
```dart
Container(
  width: 32,
  height: 32,
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(16),
  ),
  child: const Icon(Icons.mosque, color: Colors.green, size: 20),
),
```

بهذا الكود:
```dart
Container(
  width: 32,
  height: 32,
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(16),
  ),
  child: ClipRRect(
    borderRadius: BorderRadius.circular(16),
    child: Image.asset(
      'assets/logos/forgan_logo.png',
      fit: BoxFit.cover,
    ),
  ),
),
```

#### في شاشة الترحيب (lib/screens/welcome_screen.dart):
استبدل هذا الكود:
```dart
Container(
  width: 120,
  height: 120,
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(60),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.2),
        blurRadius: 15,
        offset: const Offset(0, 5),
      ),
    ],
  ),
  child: const Icon(Icons.mosque, color: Colors.green, size: 60),
),
```

بهذا الكود:
```dart
Container(
  width: 120,
  height: 120,
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(60),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.2),
        blurRadius: 15,
        offset: const Offset(0, 5),
      ),
    ],
  ),
  child: ClipRRect(
    borderRadius: BorderRadius.circular(60),
    child: Image.asset(
      'assets/logos/forgan_logo.png',
      fit: BoxFit.cover,
    ),
  ),
),
```

## ✅ التحديثات المكتملة:

1. ✅ تم تحديث اسم التطبيق إلى "المهام اليومية للحج - حملة الفرقان"
2. ✅ تم تحديث الشاشة الرئيسية لتتضمن مكان للشعار
3. ✅ تم تحديث شاشة الترحيب لتتضمن اسم الحملة والشعار
4. ⏳ يتطلب إضافة ملف الصورة الفعلي

## 📝 ملاحظات:
- الشعار الذي أرسلته جميل جداً ومناسب للتطبيق
- التصميم الدائري والألوان البنية الأنيقة ستبدو رائعة في التطبيق
- تم استخدام أيقونة مؤقتة (مسجد) حتى يتم إضافة الشعار الفعلي
