<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات التطبيق</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        
        .icon-preview {
            display: inline-block;
            margin: 20px;
            text-align: center;
        }
        
        .icon {
            width: 120px;
            height: 120px;
            border-radius: 20px;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .icon::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
        }
        
        .icon-content {
            color: white;
            font-size: 48px;
            z-index: 2;
        }
        
        .icon-small {
            width: 80px;
            height: 80px;
            border-radius: 15px;
        }
        
        .icon-small .icon-content {
            font-size: 32px;
        }
        
        .icon-text {
            font-weight: bold;
            color: #2E7D32;
            margin-top: 10px;
        }
        
        .download-section {
            margin: 40px 0;
            padding: 30px;
            background: #f5f5f5;
            border-radius: 15px;
        }
        
        .button {
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .instructions {
            text-align: right;
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .step {
            margin: 15px 0;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-right: 4px solid #4CAF50;
        }
        
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #2E7D32; margin-bottom: 10px;">🎨 أيقونات تطبيق المهام اليومية للحج</h1>
        <p style="color: #666; margin-bottom: 30px;">حملة الفرقان</p>
        
        <div style="margin: 30px 0;">
            <div class="icon-preview">
                <div class="icon" id="icon-512">
                    <div class="icon-content">🕋</div>
                </div>
                <div class="icon-text">512x512 - أيقونة رئيسية</div>
            </div>
            
            <div class="icon-preview">
                <div class="icon icon-small" id="icon-192">
                    <div class="icon-content">🕋</div>
                </div>
                <div class="icon-text">192x192 - أيقونة متوسطة</div>
            </div>
        </div>
        
        <div class="download-section">
            <h3 style="color: #2E7D32;">📥 تحميل الأيقونات</h3>
            <p>اضغط على الأزرار أدناه لتحميل الأيقونات بأحجام مختلفة:</p>
            
            <button class="button" onclick="downloadIcon(512)">
                📱 تحميل أيقونة 512x512
            </button>
            
            <button class="button" onclick="downloadIcon(192)">
                📱 تحميل أيقونة 192x192
            </button>
            
            <button class="button" onclick="downloadAllIcons()">
                📦 تحميل جميع الأيقونات
            </button>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام</h3>
            
            <div class="step">
                <strong>الخطوة 1:</strong> حمّل الأيقونات باستخدام الأزرار أعلاه
            </div>
            
            <div class="step">
                <strong>الخطوة 2:</strong> ضع الأيقونات في مجلد icons داخل web_version
            </div>
            
            <div class="step">
                <strong>الخطوة 3:</strong> استبدل الأيقونات الموجودة بالأيقونات الجديدة
            </div>
            
            <div class="step">
                <strong>الخطوة 4:</strong> ارفع التطبيق على استضافة ويب
            </div>
            
            <div class="step">
                <strong>النتيجة:</strong> ستظهر أيقونة جميلة عند إضافة التطبيق للشاشة الرئيسية
            </div>
        </div>
        
        <div style="margin-top: 30px; color: #666; font-size: 14px;">
            <p><strong>💡 نصيحة:</strong> الأيقونات مصممة بألوان التطبيق الأصلية (أخضر إسلامي)</p>
            <p><strong>🎨 التصميم:</strong> رمز الكعبة مع خلفية متدرجة أنيقة</p>
        </div>
    </div>

    <canvas id="canvas"></canvas>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2E7D32');
            
            // رسم الخلفية
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // إطار داخلي
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = size * 0.02;
            ctx.strokeRect(size * 0.1, size * 0.1, size * 0.8, size * 0.8);
            
            // رمز الكعبة
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🕋', size / 2, size / 2);
            
            return canvas;
        }
        
        function downloadIcon(size) {
            const canvas = createIcon(size);
            const link = document.createElement('a');
            link.download = `hajj-icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAllIcons() {
            const sizes = [192, 512];
            sizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size * 2);
            });
            
            alert('🎉 تم بدء تحميل جميع الأيقونات!\n\nستجد الملفات في مجلد التحميلات:\n• hajj-icon-192x192.png\n• hajj-icon-512x512.png');
        }
        
        // رسالة ترحيب
        window.onload = function() {
            console.log('🕋 مولد أيقونات تطبيق المهام اليومية للحج');
            console.log('🎨 تصميم احترافي بألوان إسلامية');
        };
    </script>
</body>
</html>
