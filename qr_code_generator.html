<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد QR Code لتطبيق الحج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2E7D32;
            margin: 0;
            font-size: 28px;
        }
        
        .header .subtitle {
            color: #666;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .kaaba-icon {
            width: 60px;
            height: 60px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 20px;
            font-size: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            direction: ltr;
        }
        
        .input-group input:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .qr-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
            display: none;
        }
        
        .qr-code {
            margin: 20px 0;
        }
        
        .download-btn {
            background: #2196F3;
            margin-top: 10px;
        }
        
        .download-btn:hover {
            background: #1976D2;
        }
        
        .instructions {
            background: #E8F5E8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            color: #2E7D32;
            margin-top: 0;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .example-urls {
            background: #FFF3E0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-right: 4px solid #FF9800;
        }
        
        .example-urls h4 {
            margin-top: 0;
            color: #E65100;
        }
        
        .example-url {
            background: white;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 14px;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="kaaba-icon">🕋</div>
            <h1>مولد QR Code لتطبيق الحج</h1>
            <div class="subtitle">حملة الفرقان 1446 هـ</div>
        </div>
        
        <div class="input-group">
            <label for="appUrl">رابط التطبيق:</label>
            <input type="url" id="appUrl" placeholder="https://your-app.netlify.app" />
        </div>
        
        <button class="btn" onclick="generateQR()">إنشاء QR Code</button>
        
        <div id="qrContainer" class="qr-container">
            <h3>QR Code جاهز للطباعة والمشاركة</h3>
            <div id="qrCode" class="qr-code"></div>
            <button class="btn download-btn" onclick="downloadQR()">تحميل QR Code</button>
        </div>
        
        <div class="example-urls">
            <h4>أمثلة على روابط الاستضافة:</h4>
            <div class="example-url">https://hajj-tasks-1446.netlify.app</div>
            <div class="example-url">https://username.github.io/hajj-app</div>
            <div class="example-url">https://hajj-app.web.app</div>
            <div class="example-url">https://hajj-app.vercel.app</div>
        </div>
        
        <div class="instructions">
            <h3>كيفية الاستخدام:</h3>
            <ul>
                <li><strong>ارفع التطبيق</strong> على استضافة مجانية (Netlify مُوصى به)</li>
                <li><strong>انسخ الرابط</strong> والصقه في الحقل أعلاه</li>
                <li><strong>اضغط "إنشاء QR Code"</strong> للحصول على الكود</li>
                <li><strong>حمّل الصورة</strong> واطبعها أو شاركها</li>
                <li><strong>ضع QR Code</strong> في المخيم ليسهل على الحجاج الوصول للتطبيق</li>
            </ul>
            
            <h3>للحجاج:</h3>
            <ul>
                <li><strong>امسح QR Code</strong> بكاميرا الهاتف</li>
                <li><strong>اضغط على الرابط</strong> الذي يظهر</li>
                <li><strong>في الآيفون:</strong> افتح بـ Safari ثم "إضافة للشاشة الرئيسية"</li>
                <li><strong>في الأندرويد:</strong> اضغط "إضافة للشاشة الرئيسية"</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        function generateQR() {
            const url = document.getElementById('appUrl').value;
            
            if (!url) {
                alert('يرجى إدخال رابط التطبيق');
                return;
            }
            
            if (!isValidUrl(url)) {
                alert('يرجى إدخال رابط صحيح يبدأ بـ https://');
                return;
            }
            
            const qrContainer = document.getElementById('qrContainer');
            const qrCodeDiv = document.getElementById('qrCode');
            
            // Clear previous QR code
            qrCodeDiv.innerHTML = '';
            
            // Generate QR code
            QRCode.toCanvas(qrCodeDiv, url, {
                width: 300,
                height: 300,
                color: {
                    dark: '#2E7D32',
                    light: '#FFFFFF'
                },
                margin: 2
            }, function (error) {
                if (error) {
                    alert('حدث خطأ في إنشاء QR Code');
                    console.error(error);
                } else {
                    qrContainer.style.display = 'block';
                    
                    // Add app info below QR code
                    const infoDiv = document.createElement('div');
                    infoDiv.innerHTML = `
                        <div style="margin-top: 15px; padding: 15px; background: white; border-radius: 8px; border: 2px solid #4CAF50;">
                            <h4 style="margin: 0 0 10px 0; color: #2E7D32;">📱 تطبيق المهام اليومية للحج</h4>
                            <p style="margin: 5px 0; color: #666;">حملة الفرقان 1446 هـ</p>
                            <p style="margin: 5px 0; font-size: 12px; color: #888; direction: ltr; text-align: center;">${url}</p>
                            <p style="margin: 10px 0 0 0; font-size: 14px; color: #333;">امسح الكود للحصول على التطبيق</p>
                        </div>
                    `;
                    qrCodeDiv.appendChild(infoDiv);
                }
            });
        }
        
        function downloadQR() {
            const canvas = document.querySelector('#qrCode canvas');
            if (!canvas) {
                alert('لا يوجد QR Code للتحميل');
                return;
            }
            
            // Create a larger canvas with app info
            const finalCanvas = document.createElement('canvas');
            const ctx = finalCanvas.getContext('2d');
            
            finalCanvas.width = 400;
            finalCanvas.height = 500;
            
            // White background
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
            
            // Draw QR code
            ctx.drawImage(canvas, 50, 50, 300, 300);
            
            // Add title
            ctx.fillStyle = '#2E7D32';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('تطبيق المهام اليومية للحج', 200, 30);
            
            // Add subtitle
            ctx.fillStyle = '#666666';
            ctx.font = '18px Arial';
            ctx.fillText('حملة الفرقان 1446 هـ', 200, 380);
            
            // Add instructions
            ctx.fillStyle = '#333333';
            ctx.font = '16px Arial';
            ctx.fillText('امسح الكود للحصول على التطبيق', 200, 410);
            
            // Add kaaba emoji (if supported)
            ctx.font = '30px Arial';
            ctx.fillText('🕋', 200, 450);
            
            // Download
            const link = document.createElement('a');
            link.download = 'hajj-app-qr-code.png';
            link.href = finalCanvas.toDataURL();
            link.click();
        }
        
        function isValidUrl(string) {
            try {
                const url = new URL(string);
                return url.protocol === 'https:';
            } catch (_) {
                return false;
            }
        }
        
        // Auto-focus on input
        document.getElementById('appUrl').focus();
    </script>
</body>
</html>
